-- Merging decision tree log ---
manifest
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:1:1-38:12
MERGED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:1:1-38:12
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-gesture-handler] D:\connecto-cli\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\connecto-cli\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\connecto-cli\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-sound] D:\connecto-cli\frontend\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\connecto-cli\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-linear-gradient] D:\connecto-cli\frontend\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] D:\connecto-cli\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-masked-view_masked-view] D:\connecto-cli\frontend\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-date-picker] D:\connecto-cli\frontend\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:react-native-incall-manager] D:\connecto-cli\frontend\node_modules\react-native-incall-manager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-splash-screen] D:\connecto-cli\frontend\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] D:\connecto-cli\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-20:12
MERGED from [:react-native-vector-icons] D:\connecto-cli\frontend\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.12.0] D:\Gradle\caches\8.13\transforms\e71c4745b60371775eb37b1c89844fe5\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] D:\Gradle\caches\8.13\transforms\3b92c746e5f101b1adbc57b317d27b62\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.4.3] D:\Gradle\caches\8.13\transforms\f7710a2674bd9394b74bf5740ae47f99\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\Gradle\caches\8.13\transforms\cacfeb9c052a37eaa3966725145c8061\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\Gradle\caches\8.13\transforms\baa6f76bf3a926da1d269cd39226a964\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\Gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] D:\Gradle\caches\8.13\transforms\04ce97cc619ff9f25f3761c411e55e26\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Gradle\caches\8.13\transforms\af44eb3b92496facf59fc6b934445127\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Gradle\caches\8.13\transforms\d6cec6608d89944da84b0f17825f1248\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] D:\Gradle\caches\8.13\transforms\57ed3893266a2b5d87a20c53003d4785\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Gradle\caches\8.13\transforms\ebe5f9d6bb22785995c8a68fad3f116c\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] D:\Gradle\caches\8.13\transforms\07558f9674a813498ccc81a40e20d95e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] D:\Gradle\caches\8.13\transforms\9fb86824cebda7e4978b87bf007fb2e4\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Gradle\caches\8.13\transforms\220fcca70ec6991a73934c5fc52e7ae6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Gradle\caches\8.13\transforms\3388d783222b7df4ed9f62284b55d44a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\Gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Gradle\caches\8.13\transforms\43f9dcbe66670ee00e8bf8aa0e20ee5a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Gradle\caches\8.13\transforms\fe10492beb5e0fd37fded09ab375eb23\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\Gradle\caches\8.13\transforms\c37b084827537f13f7041c2abca2eac0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\b2a82f4aa1a4179216c7b50844e83b1d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\d83f6611ae3c9b2d987f409e1db82327\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\4853bad00cdfdbce34ddb5e5af07713c\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Gradle\caches\8.13\transforms\5314fc185820de5c038ee410797e334b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\Gradle\caches\8.13\transforms\ba3980accbde2597dc850a1e0ae4ca96\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Gradle\caches\8.13\transforms\e681b5e970f6fcfef3a4b8f1661d80e8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:fresco:3.6.0] D:\Gradle\caches\8.13\transforms\5a6493744204d1e7ff59982ff66623cb\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] D:\Gradle\caches\8.13\transforms\5dde6eb084870ebd57e13c058d0f30d2\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] D:\Gradle\caches\8.13\transforms\6f51b3c75b0c28653364af15a42e1455\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] D:\Gradle\caches\8.13\transforms\8c47aaf467a134503f443cf19f129acf\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] D:\Gradle\caches\8.13\transforms\71f1b4a1d29cc83e40dac70e74302da3\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] D:\Gradle\caches\8.13\transforms\87c17a2e526c50babcaab147743ed237\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] D:\Gradle\caches\8.13\transforms\a7ee3e1e2befe0e2a89ea280faa89f07\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] D:\Gradle\caches\8.13\transforms\b385b7070714358c23bc5db0e8ec0474\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] D:\Gradle\caches\8.13\transforms\22702d4e7438cb8f2c6a3fc4ce3898b3\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] D:\Gradle\caches\8.13\transforms\d205e16cd4a68709c65aac5e4dbf87dc\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] D:\Gradle\caches\8.13\transforms\37cc81372e1059273267f19ac2225841\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] D:\Gradle\caches\8.13\transforms\121ff23e5c7b391bf3b9396012a3f711\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] D:\Gradle\caches\8.13\transforms\e086bcfbb5956d9176803298aea98e71\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] D:\Gradle\caches\8.13\transforms\ed2c782cc6e413c8f2f8a73ce804decb\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] D:\Gradle\caches\8.13\transforms\edb1d2c3d5443a4c517fe4df02f6dcbe\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] D:\Gradle\caches\8.13\transforms\a91336b41bf2a20205ac6d2530b3b236\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] D:\Gradle\caches\8.13\transforms\434dae9639c9d842d76f7f5728dfd429\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Gradle\caches\8.13\transforms\79de66fb252ed1d2694fb31f19d93d7e\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] D:\Gradle\caches\8.13\transforms\a170c3533db9080d66042ab5cf88e840\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\Gradle\caches\8.13\transforms\e0088f672359fac7f5c55cd440ef577a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.3] D:\Gradle\caches\8.13\transforms\917a7d068c0b038b6c3c85ca03812fc8\transformed\jetified-hermes-android-0.79.3-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] D:\Gradle\caches\8.13\transforms\a45789389ddd15c7ea71843542a8ff9d\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] D:\Gradle\caches\8.13\transforms\5b4c2e64f649e1411a5e7ccc73cd87bd\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\Gradle\caches\8.13\transforms\1dcfea128d5541f1d62ec1ae8c25cadf\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Gradle\caches\8.13\transforms\1a1ce0a30a8c0e07a7361af3639daf33\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Gradle\caches\8.13\transforms\01630efa3cc1106b523e4b2247986054\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\Gradle\caches\8.13\transforms\1e8ba872d9749ce34982f9c6932f8bc1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] D:\Gradle\caches\8.13\transforms\aa8ca875459fac9aa548f9f0a9258e68\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [net.time4j:time4j-android:4.8-2021a] D:\Gradle\caches\8.13\transforms\9d3f0ea0bcb6c4f8a605437b6d0db17e\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:2:1-13:12
MERGED from [org.jitsi:webrtc:118.0.0] D:\Gradle\caches\8.13\transforms\397ca5b2c2d171ee92863d2c4ce8f622\transformed\jetified-webrtc-118.0.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] D:\Gradle\caches\8.13\transforms\490740573f38e6ed55cd7cd0f0ce2cff\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:4:5-71
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:4:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:5:5-80
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:5:22-77
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:7:5-68
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.VIBRATE
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.CAMERA
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:9:5-65
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:9:22-62
uses-permission#android.permission.BLUETOOTH
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:10:5-68
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:11:5-74
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:11:22-71
uses-permission#android.permission.READ_PHONE_STATE
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:12:5-75
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:12:22-72
uses-permission#android.permission.CALL_PHONE
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:13:5-69
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:13:22-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:14:5-78
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:15:22-78
application
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:17:5-37:19
MERGED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:17:5-37:19
MERGED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:17:5-37:19
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-18:19
MERGED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-18:19
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.12.0] D:\Gradle\caches\8.13\transforms\e71c4745b60371775eb37b1c89844fe5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\Gradle\caches\8.13\transforms\e71c4745b60371775eb37b1c89844fe5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\Gradle\caches\8.13\transforms\baa6f76bf3a926da1d269cd39226a964\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\Gradle\caches\8.13\transforms\baa6f76bf3a926da1d269cd39226a964\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [net.time4j:time4j-android:4.8-2021a] D:\Gradle\caches\8.13\transforms\9d3f0ea0bcb6c4f8a605437b6d0db17e\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:11:5-20
MERGED from [net.time4j:time4j-android:4.8-2021a] D:\Gradle\caches\8.13\transforms\9d3f0ea0bcb6c4f8a605437b6d0db17e\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:19:7-39
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:19:7-39
	tools:ignore
		ADDED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:21:7-52
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:21:7-52
	tools:targetApi
		ADDED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:20:7-41
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:20:7-41
	android:allowBackup
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:22:7-34
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:22:7-34
	android:theme
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:23:7-38
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:23:7-38
	android:usesCleartextTraffic
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:24:7-42
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:18:7-38
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:18:7-38
activity#com.frontend.MainActivity
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:25:7-36:18
	android:launchMode
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:28:9-40
	android:windowSoftInputMode
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:31:9-51
	android:exported
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:27:9-32
	android:configChanges
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:30:9-118
	android:theme
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:29:9-43
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:26:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:32:9-35:25
action#android.intent.action.MAIN
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:33:13-65
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:33:21-62
category#android.intent.category.LAUNCHER
ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:34:13-73
	android:name
		ADDED from D:\connecto-cli\frontend\android\app\src\main\AndroidManifest.xml:34:23-70
uses-sdk
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-gesture-handler] D:\connecto-cli\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\connecto-cli\frontend\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\connecto-cli\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\connecto-cli\frontend\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\connecto-cli\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\connecto-cli\frontend\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-sound] D:\connecto-cli\frontend\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-sound] D:\connecto-cli\frontend\node_modules\react-native-sound\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\connecto-cli\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\connecto-cli\frontend\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] D:\connecto-cli\frontend\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] D:\connecto-cli\frontend\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\connecto-cli\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\connecto-cli\frontend\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] D:\connecto-cli\frontend\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] D:\connecto-cli\frontend\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-date-picker] D:\connecto-cli\frontend\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-date-picker] D:\connecto-cli\frontend\node_modules\react-native-date-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-incall-manager] D:\connecto-cli\frontend\node_modules\react-native-incall-manager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-incall-manager] D:\connecto-cli\frontend\node_modules\react-native-incall-manager\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-splash-screen] D:\connecto-cli\frontend\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-splash-screen] D:\connecto-cli\frontend\node_modules\react-native-splash-screen\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\connecto-cli\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\connecto-cli\frontend\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:react-native-vector-icons] D:\connecto-cli\frontend\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\connecto-cli\frontend\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:10:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\Gradle\caches\8.13\transforms\52356d6d90573e7f06600304672dd531\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.12.0] D:\Gradle\caches\8.13\transforms\e71c4745b60371775eb37b1c89844fe5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\Gradle\caches\8.13\transforms\e71c4745b60371775eb37b1c89844fe5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] D:\Gradle\caches\8.13\transforms\3b92c746e5f101b1adbc57b317d27b62\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] D:\Gradle\caches\8.13\transforms\3b92c746e5f101b1adbc57b317d27b62\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] D:\Gradle\caches\8.13\transforms\f7710a2674bd9394b74bf5740ae47f99\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] D:\Gradle\caches\8.13\transforms\f7710a2674bd9394b74bf5740ae47f99\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\Gradle\caches\8.13\transforms\cacfeb9c052a37eaa3966725145c8061\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\Gradle\caches\8.13\transforms\cacfeb9c052a37eaa3966725145c8061\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\Gradle\caches\8.13\transforms\baa6f76bf3a926da1d269cd39226a964\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] D:\Gradle\caches\8.13\transforms\baa6f76bf3a926da1d269cd39226a964\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\Gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\Gradle\caches\8.13\transforms\c2455bfab1cfa3eca9fababdaf610ea7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] D:\Gradle\caches\8.13\transforms\04ce97cc619ff9f25f3761c411e55e26\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] D:\Gradle\caches\8.13\transforms\04ce97cc619ff9f25f3761c411e55e26\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Gradle\caches\8.13\transforms\af44eb3b92496facf59fc6b934445127\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] D:\Gradle\caches\8.13\transforms\af44eb3b92496facf59fc6b934445127\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Gradle\caches\8.13\transforms\d6cec6608d89944da84b0f17825f1248\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] D:\Gradle\caches\8.13\transforms\d6cec6608d89944da84b0f17825f1248\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] D:\Gradle\caches\8.13\transforms\57ed3893266a2b5d87a20c53003d4785\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] D:\Gradle\caches\8.13\transforms\57ed3893266a2b5d87a20c53003d4785\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Gradle\caches\8.13\transforms\ebe5f9d6bb22785995c8a68fad3f116c\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] D:\Gradle\caches\8.13\transforms\ebe5f9d6bb22785995c8a68fad3f116c\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\Gradle\caches\8.13\transforms\07558f9674a813498ccc81a40e20d95e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] D:\Gradle\caches\8.13\transforms\07558f9674a813498ccc81a40e20d95e\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\Gradle\caches\8.13\transforms\9fb86824cebda7e4978b87bf007fb2e4\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\Gradle\caches\8.13\transforms\9fb86824cebda7e4978b87bf007fb2e4\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Gradle\caches\8.13\transforms\220fcca70ec6991a73934c5fc52e7ae6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\Gradle\caches\8.13\transforms\220fcca70ec6991a73934c5fc52e7ae6\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Gradle\caches\8.13\transforms\3388d783222b7df4ed9f62284b55d44a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\Gradle\caches\8.13\transforms\3388d783222b7df4ed9f62284b55d44a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\Gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\Gradle\caches\8.13\transforms\51b48f0baccc9e1df24529bede4282ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Gradle\caches\8.13\transforms\43f9dcbe66670ee00e8bf8aa0e20ee5a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\Gradle\caches\8.13\transforms\43f9dcbe66670ee00e8bf8aa0e20ee5a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Gradle\caches\8.13\transforms\fe10492beb5e0fd37fded09ab375eb23\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\Gradle\caches\8.13\transforms\fe10492beb5e0fd37fded09ab375eb23\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\Gradle\caches\8.13\transforms\c37b084827537f13f7041c2abca2eac0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\Gradle\caches\8.13\transforms\c37b084827537f13f7041c2abca2eac0\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\b2a82f4aa1a4179216c7b50844e83b1d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\b2a82f4aa1a4179216c7b50844e83b1d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\Gradle\caches\8.13\transforms\75c1fa56d9c0eec39556d8ccddfd61c6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\d83f6611ae3c9b2d987f409e1db82327\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\d83f6611ae3c9b2d987f409e1db82327\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\4853bad00cdfdbce34ddb5e5af07713c\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\Gradle\caches\8.13\transforms\4853bad00cdfdbce34ddb5e5af07713c\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\Gradle\caches\8.13\transforms\74de6f84e85601b7d879e8bc3606e3fe\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\Gradle\caches\8.13\transforms\138c1389f563f6539af20532c4e81b5f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\Gradle\caches\8.13\transforms\c8c1d43c6bd4bfd94f802c20e2f34ed6\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Gradle\caches\8.13\transforms\5314fc185820de5c038ee410797e334b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\Gradle\caches\8.13\transforms\5314fc185820de5c038ee410797e334b\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\Gradle\caches\8.13\transforms\ba3980accbde2597dc850a1e0ae4ca96\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\Gradle\caches\8.13\transforms\ba3980accbde2597dc850a1e0ae4ca96\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\Gradle\caches\8.13\transforms\e641725f61e4a7d4f1565785ffb49bb6\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Gradle\caches\8.13\transforms\e681b5e970f6fcfef3a4b8f1661d80e8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\Gradle\caches\8.13\transforms\e681b5e970f6fcfef3a4b8f1661d80e8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\Gradle\caches\8.13\transforms\e664fb735a92e9bf7e81f06fa1043c9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\Gradle\caches\8.13\transforms\1c1547cb4a3cfae9907fdc8ea27e52b9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:fresco:3.6.0] D:\Gradle\caches\8.13\transforms\5a6493744204d1e7ff59982ff66623cb\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] D:\Gradle\caches\8.13\transforms\5a6493744204d1e7ff59982ff66623cb\transformed\jetified-fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] D:\Gradle\caches\8.13\transforms\5dde6eb084870ebd57e13c058d0f30d2\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] D:\Gradle\caches\8.13\transforms\5dde6eb084870ebd57e13c058d0f30d2\transformed\jetified-imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] D:\Gradle\caches\8.13\transforms\6f51b3c75b0c28653364af15a42e1455\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] D:\Gradle\caches\8.13\transforms\6f51b3c75b0c28653364af15a42e1455\transformed\jetified-drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] D:\Gradle\caches\8.13\transforms\8c47aaf467a134503f443cf19f129acf\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] D:\Gradle\caches\8.13\transforms\8c47aaf467a134503f443cf19f129acf\transformed\jetified-nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] D:\Gradle\caches\8.13\transforms\71f1b4a1d29cc83e40dac70e74302da3\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] D:\Gradle\caches\8.13\transforms\71f1b4a1d29cc83e40dac70e74302da3\transformed\jetified-memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] D:\Gradle\caches\8.13\transforms\87c17a2e526c50babcaab147743ed237\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] D:\Gradle\caches\8.13\transforms\87c17a2e526c50babcaab147743ed237\transformed\jetified-memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] D:\Gradle\caches\8.13\transforms\a7ee3e1e2befe0e2a89ea280faa89f07\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] D:\Gradle\caches\8.13\transforms\a7ee3e1e2befe0e2a89ea280faa89f07\transformed\jetified-imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] D:\Gradle\caches\8.13\transforms\b385b7070714358c23bc5db0e8ec0474\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] D:\Gradle\caches\8.13\transforms\b385b7070714358c23bc5db0e8ec0474\transformed\jetified-memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] D:\Gradle\caches\8.13\transforms\22702d4e7438cb8f2c6a3fc4ce3898b3\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] D:\Gradle\caches\8.13\transforms\22702d4e7438cb8f2c6a3fc4ce3898b3\transformed\jetified-imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] D:\Gradle\caches\8.13\transforms\d205e16cd4a68709c65aac5e4dbf87dc\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] D:\Gradle\caches\8.13\transforms\d205e16cd4a68709c65aac5e4dbf87dc\transformed\jetified-nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] D:\Gradle\caches\8.13\transforms\37cc81372e1059273267f19ac2225841\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] D:\Gradle\caches\8.13\transforms\37cc81372e1059273267f19ac2225841\transformed\jetified-imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] D:\Gradle\caches\8.13\transforms\121ff23e5c7b391bf3b9396012a3f711\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] D:\Gradle\caches\8.13\transforms\121ff23e5c7b391bf3b9396012a3f711\transformed\jetified-urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] D:\Gradle\caches\8.13\transforms\e086bcfbb5956d9176803298aea98e71\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] D:\Gradle\caches\8.13\transforms\e086bcfbb5956d9176803298aea98e71\transformed\jetified-vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] D:\Gradle\caches\8.13\transforms\ed2c782cc6e413c8f2f8a73ce804decb\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] D:\Gradle\caches\8.13\transforms\ed2c782cc6e413c8f2f8a73ce804decb\transformed\jetified-middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] D:\Gradle\caches\8.13\transforms\edb1d2c3d5443a4c517fe4df02f6dcbe\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] D:\Gradle\caches\8.13\transforms\edb1d2c3d5443a4c517fe4df02f6dcbe\transformed\jetified-ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] D:\Gradle\caches\8.13\transforms\a91336b41bf2a20205ac6d2530b3b236\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] D:\Gradle\caches\8.13\transforms\a91336b41bf2a20205ac6d2530b3b236\transformed\jetified-soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] D:\Gradle\caches\8.13\transforms\434dae9639c9d842d76f7f5728dfd429\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] D:\Gradle\caches\8.13\transforms\434dae9639c9d842d76f7f5728dfd429\transformed\jetified-fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Gradle\caches\8.13\transforms\79de66fb252ed1d2694fb31f19d93d7e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] D:\Gradle\caches\8.13\transforms\79de66fb252ed1d2694fb31f19d93d7e\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\Gradle\caches\8.13\transforms\a170c3533db9080d66042ab5cf88e840\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\Gradle\caches\8.13\transforms\a170c3533db9080d66042ab5cf88e840\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\Gradle\caches\8.13\transforms\e0088f672359fac7f5c55cd440ef577a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] D:\Gradle\caches\8.13\transforms\e0088f672359fac7f5c55cd440ef577a\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.3] D:\Gradle\caches\8.13\transforms\917a7d068c0b038b6c3c85ca03812fc8\transformed\jetified-hermes-android-0.79.3-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.3] D:\Gradle\caches\8.13\transforms\917a7d068c0b038b6c3c85ca03812fc8\transformed\jetified-hermes-android-0.79.3-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.3] D:\Gradle\caches\8.13\transforms\a45789389ddd15c7ea71843542a8ff9d\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] D:\Gradle\caches\8.13\transforms\a45789389ddd15c7ea71843542a8ff9d\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] D:\Gradle\caches\8.13\transforms\5b4c2e64f649e1411a5e7ccc73cd87bd\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] D:\Gradle\caches\8.13\transforms\5b4c2e64f649e1411a5e7ccc73cd87bd\transformed\jetified-tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\Gradle\caches\8.13\transforms\0359dc3e4beba4c31dab86cb5151fe11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\Gradle\caches\8.13\transforms\466397756129ae51d43132ac2a7a4cd0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] D:\Gradle\caches\8.13\transforms\1dcfea128d5541f1d62ec1ae8c25cadf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\Gradle\caches\8.13\transforms\1dcfea128d5541f1d62ec1ae8c25cadf\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\Gradle\caches\8.13\transforms\3ccbae0f41e442baf2e59eea5c738174\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\Gradle\caches\8.13\transforms\0ee5b28c5a7092e8c10963bb6da395a3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Gradle\caches\8.13\transforms\1a1ce0a30a8c0e07a7361af3639daf33\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\Gradle\caches\8.13\transforms\1a1ce0a30a8c0e07a7361af3639daf33\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Gradle\caches\8.13\transforms\01630efa3cc1106b523e4b2247986054\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\Gradle\caches\8.13\transforms\01630efa3cc1106b523e4b2247986054\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Gradle\caches\8.13\transforms\1e8ba872d9749ce34982f9c6932f8bc1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\Gradle\caches\8.13\transforms\1e8ba872d9749ce34982f9c6932f8bc1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] D:\Gradle\caches\8.13\transforms\aa8ca875459fac9aa548f9f0a9258e68\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] D:\Gradle\caches\8.13\transforms\aa8ca875459fac9aa548f9f0a9258e68\transformed\jetified-ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [net.time4j:time4j-android:4.8-2021a] D:\Gradle\caches\8.13\transforms\9d3f0ea0bcb6c4f8a605437b6d0db17e\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:7:5-9:41
MERGED from [net.time4j:time4j-android:4.8-2021a] D:\Gradle\caches\8.13\transforms\9d3f0ea0bcb6c4f8a605437b6d0db17e\transformed\jetified-time4j-android-4.8-2021a\AndroidManifest.xml:7:5-9:41
MERGED from [org.jitsi:webrtc:118.0.0] D:\Gradle\caches\8.13\transforms\397ca5b2c2d171ee92863d2c4ce8f622\transformed\jetified-webrtc-118.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [org.jitsi:webrtc:118.0.0] D:\Gradle\caches\8.13\transforms\397ca5b2c2d171ee92863d2c4ce8f622\transformed\jetified-webrtc-118.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.fbjni:fbjni:0.7.0] D:\Gradle\caches\8.13\transforms\490740573f38e6ed55cd7cd0f0ce2cff\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] D:\Gradle\caches\8.13\transforms\490740573f38e6ed55cd7cd0f0ce2cff\transformed\jetified-fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\connecto-cli\frontend\android\app\src\debug\AndroidManifest.xml
provider#com.imagepicker.ImagePickerProvider
ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
	android:exported
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
	android:resource
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
	android:name
		ADDED from [:react-native-image-picker] D:\connecto-cli\frontend\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
uses-feature#android.hardware.usb.host
ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-11:32
	tools:node
		ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-29
	android:required
		ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-33
	android:name
		ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-49
service#com.oney.WebRTCModule.MediaProjectionService
ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:19
	android:foregroundServiceType
		ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-60
	android:name
		ADDED from [:react-native-webrtc] D:\connecto-cli\frontend\node_modules\react-native-webrtc\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-72
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.3] D:\Gradle\caches\8.13\transforms\f83041c7687ee733409179db57037f5d\transformed\jetified-react-android-0.79.3-debug\AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\Gradle\caches\8.13\transforms\4d628386376b0c97db6dfd4a1aef2967\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\Gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\Gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.frontend.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.frontend.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\Gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] D:\Gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] D:\Gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
