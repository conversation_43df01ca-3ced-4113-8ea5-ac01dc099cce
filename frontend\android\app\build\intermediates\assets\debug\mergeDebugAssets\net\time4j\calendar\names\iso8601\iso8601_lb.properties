# months
M(a)_1=Jan.
M(a)_2=Feb.
M(a)_3=Mäe.
M(a)_4=Abr.
M(a)_5=Mee
M(a)_6=Juni
M(a)_7=Juli
M(a)_8=Aug.
M(a)_9=Sep.
M(a)_10=Okt.
M(a)_11=Nov.
M(a)_12=Dez.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=Januar
M(w)_2=Februar
M(w)_3=Mäerz
M(w)_4=Abrëll
M(w)_5=Mee
M(w)_6=Juni
M(w)_7=Juli
M(w)_8=August
M(w)_9=September
M(w)_10=Oktober
M(w)_11=November
M(w)_12=Dezember

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mäe
M(A)_4=Abr
M(A)_5=Mee
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Aug
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Dez

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januar
M(W)_2=Februar
M(W)_3=Mäerz
M(W)_4=Abrëll
M(W)_5=Mee
M(W)_6=Juni
M(W)_7=Juli
M(W)_8=August
M(W)_9=September
M(W)_10=Oktober
M(W)_11=November
M(W)_12=Dezember

# weekdays
D(a)_1=Méi.
D(a)_2=Dën.
D(a)_3=Mët.
D(a)_4=Don.
D(a)_5=Fre.
D(a)_6=Sam.
D(a)_7=Son.

D(n)_1=M
D(n)_2=D
D(n)_3=M
D(n)_4=D
D(n)_5=F
D(n)_6=S
D(n)_7=S

D(s)_1=Mé.
D(s)_2=Dë.
D(s)_3=Më.
D(s)_4=Do.
D(s)_5=Fr.
D(s)_6=Sa.
D(s)_7=So.

D(w)_1=Méindeg
D(w)_2=Dënschdeg
D(w)_3=Mëttwoch
D(w)_4=Donneschdeg
D(w)_5=Freideg
D(w)_6=Samschdeg
D(w)_7=Sonndeg

D(A)_1=Méi
D(A)_2=Dën
D(A)_3=Mët
D(A)_4=Don
D(A)_5=Fre
D(A)_6=Sam
D(A)_7=Son

D(N)_1=M
D(N)_2=D
D(N)_3=M
D(N)_4=D
D(N)_5=F
D(N)_6=S
D(N)_7=S

D(S)_1=Mé.
D(S)_2=Dë.
D(S)_3=Më.
D(S)_4=Do.
D(S)_5=Fr.
D(S)_6=Sa.
D(S)_7=So.

D(W)_1=Méindeg
D(W)_2=Dënschdeg
D(W)_3=Mëttwoch
D(W)_4=Donneschdeg
D(W)_5=Freideg
D(W)_6=Samschdeg
D(W)_7=Sonndeg

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. Quartal
Q(w)_2=2. Quartal
Q(w)_3=3. Quartal
Q(w)_4=4. Quartal

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. Quartal
Q(W)_2=2. Quartal
Q(W)_3=3. Quartal
Q(W)_4=4. Quartal

# day-period-translations
P(a)_am=moies
P(a)_pm=nomëttes

P(n)_am=mo.
P(n)_pm=nomë.

P(w)_am=moies
P(w)_pm=nomëttes

# eras
E(w)_0=v. Chr.
E(w)_1=n. Chr.

E(a)_0=v. Chr.
E(a|alt)_0=v. e. Z.
E(a)_1=n. Chr.
E(a|alt)_1=n. e. Z.

# format patterns
F(f)_d=EEEE, d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d. MMM y
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}
F_h=h a
F_H=HH 'Auer'
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M.
F_MMMd=d. MMM
F_y=y
F_yM=M.y
F_yMMM=MMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y

I={0} - {1}

# labels of elements
L_era=Epoch
L_year=Joer
L_quarter=Quartal
L_month=Mount
L_week=Woch
L_day=Dag
L_weekday=Wochendag
L_dayperiod=Dageshallschent
L_hour=Stonn
L_minute=Minutt
L_second=Sekonn
L_zone=Zäitzon
