# months
M(a)_1=ಜನವರಿ
M(a)_2=ಫೆಬ್ರವರಿ
M(a)_3=ಮಾರ್ಚ್
M(a)_4=ಏಪ್ರಿ
M(a)_5=ಮೇ
M(a)_6=ಜೂನ್
M(a)_7=ಜುಲೈ
M(a)_8=ಆಗ
M(a)_9=ಸೆಪ್ಟೆಂ
M(a)_10=ಅಕ್ಟೋ
M(a)_11=ನವೆಂ
M(a)_12=ಡಿಸೆಂ

M(n)_1=ಜ
M(n)_2=ಫೆ
M(n)_3=ಮಾ
M(n)_4=ಏ
M(n)_5=ಮೇ
M(n)_6=ಜೂ
M(n)_7=ಜು
M(n)_8=ಆ
M(n)_9=ಸೆ
M(n)_10=ಅ
M(n)_11=ನ
M(n)_12=ಡಿ

M(w)_1=ಜನವರಿ
M(w)_2=ಫೆಬ್ರವರಿ
M(w)_3=ಮಾರ್ಚ್
M(w)_4=ಏಪ್ರಿಲ್
M(w)_5=ಮೇ
M(w)_6=ಜೂನ್
M(w)_7=ಜುಲೈ
M(w)_8=ಆಗಸ್ಟ್
M(w)_9=ಸೆಪ್ಟೆಂಬರ್
M(w)_10=ಅಕ್ಟೋಬರ್
M(w)_11=ನವೆಂಬರ್
M(w)_12=ಡಿಸೆಂಬರ್

M(A)_1=ಜನ
M(A)_2=ಫೆಬ್ರ
M(A)_3=ಮಾರ್ಚ್
M(A)_4=ಏಪ್ರಿ
M(A)_5=ಮೇ
M(A)_6=ಜೂನ್
M(A)_7=ಜುಲೈ
M(A)_8=ಆಗ
M(A)_9=ಸೆಪ್ಟೆಂ
M(A)_10=ಅಕ್ಟೋ
M(A)_11=ನವೆಂ
M(A)_12=ಡಿಸೆಂ

M(N)_1=ಜ
M(N)_2=ಫೆ
M(N)_3=ಮಾ
M(N)_4=ಏ
M(N)_5=ಮೇ
M(N)_6=ಜೂ
M(N)_7=ಜು
M(N)_8=ಆ
M(N)_9=ಸೆ
M(N)_10=ಅ
M(N)_11=ನ
M(N)_12=ಡಿ

M(W)_1=ಜನವರಿ
M(W)_2=ಫೆಬ್ರವರಿ
M(W)_3=ಮಾರ್ಚ್
M(W)_4=ಏಪ್ರಿಲ್
M(W)_5=ಮೇ
M(W)_6=ಜೂನ್
M(W)_7=ಜುಲೈ
M(W)_8=ಆಗಸ್ಟ್
M(W)_9=ಸೆಪ್ಟೆಂಬರ್
M(W)_10=ಅಕ್ಟೋಬರ್
M(W)_11=ನವೆಂಬರ್
M(W)_12=ಡಿಸೆಂಬರ್

# weekdays
D(a)_1=ಸೋಮ
D(a)_2=ಮಂಗಳ
D(a)_3=ಬುಧ
D(a)_4=ಗುರು
D(a)_5=ಶುಕ್ರ
D(a)_6=ಶನಿ
D(a)_7=ಭಾನು

D(n)_1=ಸೋ
D(n)_2=ಮಂ
D(n)_3=ಬು
D(n)_4=ಗು
D(n)_5=ಶು
D(n)_6=ಶ
D(n)_7=ಭಾ

D(s)_1=ಸೋಮ
D(s)_2=ಮಂಗಳ
D(s)_3=ಬುಧ
D(s)_4=ಗುರು
D(s)_5=ಶುಕ್ರ
D(s)_6=ಶನಿ
D(s)_7=ಭಾನು

D(w)_1=ಸೋಮವಾರ
D(w)_2=ಮಂಗಳವಾರ
D(w)_3=ಬುಧವಾರ
D(w)_4=ಗುರುವಾರ
D(w)_5=ಶುಕ್ರವಾರ
D(w)_6=ಶನಿವಾರ
D(w)_7=ಭಾನುವಾರ

D(A)_1=ಸೋಮ
D(A)_2=ಮಂಗಳ
D(A)_3=ಬುಧ
D(A)_4=ಗುರು
D(A)_5=ಶುಕ್ರ
D(A)_6=ಶನಿ
D(A)_7=ಭಾನು

D(N)_1=ಸೋ
D(N)_2=ಮಂ
D(N)_3=ಬು
D(N)_4=ಗು
D(N)_5=ಶು
D(N)_6=ಶ
D(N)_7=ಭಾ

D(S)_1=ಸೋಮ
D(S)_2=ಮಂಗಳ
D(S)_3=ಬುಧ
D(S)_4=ಗುರು
D(S)_5=ಶುಕ್ರ
D(S)_6=ಶನಿ
D(S)_7=ಭಾನು

D(W)_1=ಸೋಮವಾರ
D(W)_2=ಮಂಗಳವಾರ
D(W)_3=ಬುಧವಾರ
D(W)_4=ಗುರುವಾರ
D(W)_5=ಶುಕ್ರವಾರ
D(W)_6=ಶನಿವಾರ
D(W)_7=ಭಾನುವಾರ

# quarters
Q(a)_1=ತ್ರೈ 1
Q(a)_2=ತ್ರೈ 2
Q(a)_3=ತ್ರೈ 3
Q(a)_4=ತ್ರೈ 4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1ನೇ ತ್ರೈಮಾಸಿಕ
Q(w)_2=2ನೇ ತ್ರೈಮಾಸಿಕ
Q(w)_3=3ನೇ ತ್ರೈಮಾಸಿಕ
Q(w)_4=4ನೇ ತ್ರೈಮಾಸಿಕ

Q(A)_1=ತ್ರೈ 1
Q(A)_2=ತ್ರೈ 2
Q(A)_3=ತ್ರೈ 3
Q(A)_4=ತ್ರೈ 4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1ನೇ ತ್ರೈಮಾಸಿಕ
Q(W)_2=2ನೇ ತ್ರೈಮಾಸಿಕ
Q(W)_3=3ನೇ ತ್ರೈಮಾಸಿಕ
Q(W)_4=4ನೇ ತ್ರೈಮಾಸಿಕ

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=ಮಧ್ಯ ರಾತ್ರಿ
P(a)_am=ಪೂರ್ವಾಹ್ನ
P(a)_pm=ಅಪರಾಹ್ನ
P(a)_morning1=ಬೆಳಗ್ಗೆ
P(a)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(a)_evening1=ಸಂಜೆ
P(a)_night1=ರಾತ್ರಿ

P(n)_midnight=ಮಧ್ಯರಾತ್ರಿ
P(n)_am=ಪೂ
P(n)_pm=ಅ
P(n)_morning1=ಬೆಳಗ್ಗೆ
P(n)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(n)_evening1=ಸಂಜೆ
P(n)_night1=ರಾತ್ರಿ

P(w)_midnight=ಮಧ್ಯ ರಾತ್ರಿ
P(w)_am=ಪೂರ್ವಾಹ್ನ
P(w)_pm=ಅಪರಾಹ್ನ
P(w)_morning1=ಬೆಳಗ್ಗೆ
P(w)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(w)_evening1=ಸಂಜೆ
P(w)_night1=ರಾತ್ರಿ

P(A)_midnight=ಮಧ್ಯರಾತ್ರಿ
P(A)_am=ಪೂರ್ವಾಹ್ನ
P(A)_pm=ಅಪರಾಹ್ನ
P(A)_morning1=ಬೆಳಗ್ಗೆ
P(A)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(A)_evening1=ಸಂಜೆ
P(A)_night1=ರಾತ್ರಿ

P(N)_midnight=ಮಧ್ಯರಾತ್ರಿ
P(N)_am=ಪೂರ್ವಾಹ್ನ
P(N)_pm=ಅಪರಾಹ್ನ
P(N)_morning1=ಬೆಳಗ್ಗೆ
P(N)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(N)_evening1=ಸಂಜೆ
P(N)_night1=ರಾತ್ರಿ

P(W)_midnight=ಮಧ್ಯರಾತ್ರಿ
P(W)_am=ಪೂರ್ವಾಹ್ನ
P(W)_pm=ಅಪರಾಹ್ನ
P(W)_morning1=ಬೆಳಗ್ಗೆ
P(W)_afternoon1=ಮಧ್ಯಾಹ್ನ
P(W)_evening1=ಸಂಜೆ
P(W)_night1=ರಾತ್ರಿ

# eras
E(w)_0=ಕ್ರಿಸ್ತ ಪೂರ್ವ
E(w|alt)_0=ಕ್ರಿ.ಪೂ.ಕಾಲ
E(w)_1=ಕ್ರಿಸ್ತ ಶಕ
E(w|alt)_1=ಪ್ರಸಕ್ತ ಶಕ

E(a)_0=ಕ್ರಿ.ಪೂ
E(a|alt)_0=ಕ್ರಿ.ಪೂ.ಕಾಲ
E(a)_1=ಕ್ರಿ.ಶ
E(a|alt)_1=ಪ್ರಸಕ್ತ ಶಕ

# format patterns
F(f)_d=EEEE, MMMM d, y
F(l)_d=MMMM d, y
F(m)_d=MMM d, y
F(s)_d=d/M/yy

F(alt)=hh:mm:ss a

F(f)_t=hh:mm:ss a zzzz
F(l)_t=hh:mm:ss a z
F(m)_t=hh:mm:ss a
F(s)_t=hh:mm a

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=MMM d
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMM=MM-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y

I={0} – {1}

# labels of elements
L_era=ಯುಗ
L_year=ವರ್ಷ
L_quarter=ತ್ರೈಮಾಸಿಕ
L_month=ತಿಂಗಳು
L_week=ವಾರ
L_day=ದಿನ
L_weekday=ವಾರದ ದಿನ
L_dayperiod=AM/PM
L_hour=ಗಂಟೆ
L_minute=ನಿಮಿಷ
L_second=ಸೆಕೆಂಡ್
L_zone=ಸಮಯ ವಲಯ
