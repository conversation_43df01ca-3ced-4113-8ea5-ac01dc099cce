# months
M(a)_1=იან
M(a)_2=თებ
M(a)_3=მარ
M(a)_4=აპრ
M(a)_5=მაი
M(a)_6=ივნ
M(a)_7=ივლ
M(a)_8=აგვ
M(a)_9=სექ
M(a)_10=ოქტ
M(a)_11=ნოე
M(a)_12=დეკ

M(n)_1=ი
M(n)_2=თ
M(n)_3=მ
M(n)_4=ა
M(n)_5=მ
M(n)_6=ი
M(n)_7=ი
M(n)_8=ა
M(n)_9=ს
M(n)_10=ო
M(n)_11=ნ
M(n)_12=დ

M(w)_1=იანვარი
M(w)_2=თებერვალი
M(w)_3=მარტი
M(w)_4=აპრილი
M(w)_5=მაისი
M(w)_6=ივნისი
M(w)_7=ივლისი
M(w)_8=აგვისტო
M(w)_9=სექტემბერი
M(w)_10=ოქტომბერი
M(w)_11=ნოემბერი
M(w)_12=დეკემბერი

M(A)_1=იან
M(A)_2=თებ
M(A)_3=მარ
M(A)_4=აპრ
M(A)_5=მაი
M(A)_6=ივნ
M(A)_7=ივლ
M(A)_8=აგვ
M(A)_9=სექ
M(A)_10=ოქტ
M(A)_11=ნოე
M(A)_12=დეკ

M(N)_1=ი
M(N)_2=თ
M(N)_3=მ
M(N)_4=ა
M(N)_5=მ
M(N)_6=ი
M(N)_7=ი
M(N)_8=ა
M(N)_9=ს
M(N)_10=ო
M(N)_11=ნ
M(N)_12=დ

M(W)_1=იანვარი
M(W)_2=თებერვალი
M(W)_3=მარტი
M(W)_4=აპრილი
M(W)_5=მაისი
M(W)_6=ივნისი
M(W)_7=ივლისი
M(W)_8=აგვისტო
M(W)_9=სექტემბერი
M(W)_10=ოქტომბერი
M(W)_11=ნოემბერი
M(W)_12=დეკემბერი

# weekdays
D(a)_1=ორშ
D(a)_2=სამ
D(a)_3=ოთხ
D(a)_4=ხუთ
D(a)_5=პარ
D(a)_6=შაბ
D(a)_7=კვი

D(n)_1=ო
D(n)_2=ს
D(n)_3=ო
D(n)_4=ხ
D(n)_5=პ
D(n)_6=შ
D(n)_7=კ

D(s)_1=ორ
D(s)_2=სმ
D(s)_3=ოთ
D(s)_4=ხთ
D(s)_5=პრ
D(s)_6=შბ
D(s)_7=კვ

D(w)_1=ორშაბათი
D(w)_2=სამშაბათი
D(w)_3=ოთხშაბათი
D(w)_4=ხუთშაბათი
D(w)_5=პარასკევი
D(w)_6=შაბათი
D(w)_7=კვირა

D(A)_1=ორშ
D(A)_2=სამ
D(A)_3=ოთხ
D(A)_4=ხუთ
D(A)_5=პარ
D(A)_6=შაბ
D(A)_7=კვი

D(N)_1=ო
D(N)_2=ს
D(N)_3=ო
D(N)_4=ხ
D(N)_5=პ
D(N)_6=შ
D(N)_7=კ

D(S)_1=ორ
D(S)_2=სმ
D(S)_3=ოთ
D(S)_4=ხთ
D(S)_5=პრ
D(S)_6=შბ
D(S)_7=კვ

D(W)_1=ორშაბათი
D(W)_2=სამშაბათი
D(W)_3=ოთხშაბათი
D(W)_4=ხუთშაბათი
D(W)_5=პარასკევი
D(W)_6=შაბათი
D(W)_7=კვირა

# quarters
Q(a)_1=I კვ.
Q(a)_2=II კვ.
Q(a)_3=III კვ.
Q(a)_4=IV კვ.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=I კვარტალი
Q(w)_2=II კვარტალი
Q(w)_3=III კვარტალი
Q(w)_4=IV კვარტალი

Q(A)_1=I კვ.
Q(A)_2=II კვ.
Q(A)_3=III კვ.
Q(A)_4=IV კვ.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=I კვარტალი
Q(W)_2=II კვარტალი
Q(W)_3=III კვარტალი
Q(W)_4=IV კვარტალი

# day-period-rules
T0500=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=შუაღამეს
P(a)_am=AM
P(a)_noon=შუადღ.
P(a)_pm=PM
P(a)_morning1=დილ.
P(a)_afternoon1=ნაშუადღ.
P(a)_evening1=საღ.
P(a)_night1=ღამ.

P(n)_midnight=შუაღამეს
P(n)_am=a
P(n)_noon=შუადღ.
P(n)_pm=p
P(n)_morning1=დილ.
P(n)_afternoon1=ნაშუადღ.
P(n)_evening1=საღ.
P(n)_night1=ღამ.

P(w)_midnight=შუაღამეს
P(w)_am=AM
P(w)_noon=შუადღეს
P(w)_pm=PM
P(w)_morning1=დილით
P(w)_afternoon1=ნაშუადღევს
P(w)_evening1=საღამოს
P(w)_night1=ღამით

P(A)_midnight=შუაღამე
P(A)_am=AM
P(A)_noon=შუადღე
P(A)_pm=PM
P(A)_morning1=დილა
P(A)_afternoon1=ნაშუადღევი
P(A)_evening1=საღამო
P(A)_night1=ღამე

P(N)_midnight=შუაღამე
P(N)_am=AM
P(N)_noon=შუადღე
P(N)_pm=PM
P(N)_morning1=დილა
P(N)_afternoon1=ნაშუადღევი
P(N)_evening1=საღამო
P(N)_night1=ღამე

P(W)_midnight=შუაღამე
P(W)_am=AM
P(W)_noon=შუადღე
P(W)_pm=შუადღ. შემდეგ
P(W)_morning1=დილა
P(W)_afternoon1=ნაშუადღევი
P(W)_evening1=საღამო
P(W)_night1=ღამე

# eras
E(w)_0=ძველი წელთაღრიცხვით
E(w|alt)_0=ჩვენს ერამდე
E(w)_1=ახალი წელთაღრიცხვით
E(w|alt)_1=ჩვენი ერა

E(a)_0=ძვ. წ.
E(a|alt)_0=ჩვ. ერამდე
E(a)_1=ახ. წ.
E(a|alt)_1=ჩვ. ერა

# format patterns
F(f)_d=EEEE, dd MMMM, y
F(l)_d=d MMMM, y
F(m)_d=d MMM. y
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M.y
F_yMMM=MMM. y
F_yMMMM=MMMM, y
F_yQQQ=QQQ, y
F_yQQQQ=QQQQ, y
F_yw=კვირა w, Y

I={0} – {1}

# labels of elements
L_era=ეპოქა
L_year=წელი
L_quarter=კვარტალი
L_month=თვე
L_week=კვირა
L_day=დღე
L_weekday=კვირის დღე
L_dayperiod=დღის ნახევარი
L_hour=საათი
L_minute=წუთი
L_second=წამი
L_zone=დროის სარტყელი
