
# property key format (first letter only if true)
useShortKeys=true

# months
M(a)_1=M1
M(a)_2=M2
M(a)_3=M3
M(a)_4=M4
M(a)_5=M5
M(a)_6=M6
M(a)_7=M7
M(a)_8=M8
M(a)_9=M9
M(a)_10=M10
M(a)_11=M11
M(a)_12=M12

M(w)_1=M01
M(w)_2=M02
M(w)_3=M03
M(w)_4=M04
M(w)_5=M05
M(w)_6=M06
M(w)_7=M07
M(w)_8=M08
M(w)_9=M09
M(w)_10=M10
M(w)_11=M11
M(w)_12=M12

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

# chinese zodiacs
zodiac-1=zi
zodiac-2=chou
zodiac-3=yin
zodiac-4=mao
zodiac-5=chen
zodiac-6=si
zodiac-7=wu
zodiac-8=wei
zodiac-9=shen
zodiac-10=you
zodiac-11=xu
zodiac-12=hai

# format patterns
F(f)=r(U) MMMM d, EEEE
F(l)=r(U) MMMM d
F(m)=r MMM d
F(s)=r-MM-dd
