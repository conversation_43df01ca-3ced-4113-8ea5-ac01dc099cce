# months
M(a)_1=de gen.
M(a)_2=de febr.
M(a)_3=de març
M(a)_4=d’abr.
M(a)_5=de maig
M(a)_6=de juny
M(a)_7=de jul.
M(a)_8=d’ag.
M(a)_9=de set.
M(a)_10=d’oct.
M(a)_11=de nov.
M(a)_12=de des.

M(n)_1=GN
M(n)_2=FB
M(n)_3=MÇ
M(n)_4=AB
M(n)_5=MG
M(n)_6=JN
M(n)_7=JL
M(n)_8=AG
M(n)_9=ST
M(n)_10=OC
M(n)_11=NV
M(n)_12=DS

M(w)_1=de gener
M(w)_2=de febrer
M(w)_3=de març
M(w)_4=d’abril
M(w)_5=de maig
M(w)_6=de juny
M(w)_7=de juliol
M(w)_8=d’agost
M(w)_9=de setembre
M(w)_10=d’octubre
M(w)_11=de novembre
M(w)_12=de desembre

M(A)_1=gen.
M(A)_2=febr.
M(A)_3=març
M(A)_4=abr.
M(A)_5=maig
M(A)_6=juny
M(A)_7=jul.
M(A)_8=ag.
M(A)_9=set.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=des.

M(N)_1=GN
M(N)_2=FB
M(N)_3=MÇ
M(N)_4=AB
M(N)_5=MG
M(N)_6=JN
M(N)_7=JL
M(N)_8=AG
M(N)_9=ST
M(N)_10=OC
M(N)_11=NV
M(N)_12=DS

M(W)_1=gener
M(W)_2=febrer
M(W)_3=març
M(W)_4=abril
M(W)_5=maig
M(W)_6=juny
M(W)_7=juliol
M(W)_8=agost
M(W)_9=setembre
M(W)_10=octubre
M(W)_11=novembre
M(W)_12=desembre

# weekdays
D(a)_1=dl.
D(a)_2=dt.
D(a)_3=dc.
D(a)_4=dj.
D(a)_5=dv.
D(a)_6=ds.
D(a)_7=dg.

D(n)_1=dl
D(n)_2=dt
D(n)_3=dc
D(n)_4=dj
D(n)_5=dv
D(n)_6=ds
D(n)_7=dg

D(s)_1=dl.
D(s)_2=dt.
D(s)_3=dc.
D(s)_4=dj.
D(s)_5=dv.
D(s)_6=ds.
D(s)_7=dg.

D(w)_1=dilluns
D(w)_2=dimarts
D(w)_3=dimecres
D(w)_4=dijous
D(w)_5=divendres
D(w)_6=dissabte
D(w)_7=diumenge

D(A)_1=dl.
D(A)_2=dt.
D(A)_3=dc.
D(A)_4=dj.
D(A)_5=dv.
D(A)_6=ds.
D(A)_7=dg.

D(N)_1=dl
D(N)_2=dt
D(N)_3=dc
D(N)_4=dj
D(N)_5=dv
D(N)_6=ds
D(N)_7=dg

D(S)_1=dl.
D(S)_2=dt.
D(S)_3=dc.
D(S)_4=dj.
D(S)_5=dv.
D(S)_6=ds.
D(S)_7=dg.

D(W)_1=dilluns
D(W)_2=dimarts
D(W)_3=dimecres
D(W)_4=dijous
D(W)_5=divendres
D(W)_6=dissabte
D(W)_7=diumenge

# quarters
Q(a)_1=1T
Q(a)_2=2T
Q(a)_3=3T
Q(a)_4=4T

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1r trimestre
Q(w)_2=2n trimestre
Q(w)_3=3r trimestre
Q(w)_4=4t trimestre

Q(A)_1=1T
Q(A)_2=2T
Q(A)_3=3T
Q(A)_4=4T

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1r trimestre
Q(W)_2=2n trimestre
Q(W)_3=3r trimestre
Q(W)_4=4t trimestre

# day-period-rules
T0000=morning1
T0600=morning2
T1200=afternoon1
T1300=afternoon2
T1900=evening1
T2100=night1

# day-period-translations
P(a)_midnight=mitjanit
P(a)_am=a. m.
P(a)_pm=p. m.
P(a)_morning1=matinada
P(a)_morning2=matí
P(a)_afternoon1=migdia
P(a)_afternoon2=tarda
P(a)_evening1=vespre
P(a)_night1=nit

P(n)_midnight=mitjanit
P(n)_am=a. m.
P(n)_pm=p. m.
P(n)_morning1=mat.
P(n)_morning2=matí
P(n)_afternoon1=md
P(n)_afternoon2=tarda
P(n)_evening1=vespre
P(n)_night1=nit

P(w)_midnight=mitjanit
P(w)_am=a. m.
P(w)_pm=p. m.
P(w)_morning1=matinada
P(w)_morning2=matí
P(w)_afternoon1=migdia
P(w)_afternoon2=tarda
P(w)_evening1=vespre
P(w)_night1=nit

P(A)_midnight=mitjanit
P(A)_am=a. m.
P(A)_pm=p. m.
P(A)_morning1=matinada
P(A)_morning2=matí
P(A)_afternoon1=migdia
P(A)_afternoon2=tarda
P(A)_evening1=vespre
P(A)_night1=nit

P(N)_midnight=mitjanit
P(N)_am=a. m.
P(N)_pm=p. m.
P(N)_morning1=matinada
P(N)_morning2=matí
P(N)_afternoon1=migdia
P(N)_afternoon2=tarda
P(N)_evening1=vespre
P(N)_night1=nit

P(W)_midnight=mitjanit
P(W)_am=a. m.
P(W)_pm=p. m.
P(W)_morning1=matinada
P(W)_morning2=matí
P(W)_afternoon1=migdia
P(W)_afternoon2=tarda
P(W)_evening1=vespre
P(W)_night1=nit

# eras
E(w)_0=abans de Crist
E(w|alt)_0=abans de l’era cristiana
E(w)_1=després de Crist
E(w|alt)_1=era cristiana

E(a)_0=aC
E(a|alt)_0=AEC
E(a)_1=dC
E(a|alt)_1=EC

E(n)_0=aC
E(n|alt)_0=AEC
E(n)_1=dC
E(n|alt)_1=EC

# format patterns
F(f)_d=EEEE, d MMMM 'de' y
F(l)_d=d MMMM 'de' y
F(m)_d=d MMM y
F(s)_d=d/M/yy

F(alt)=HH:mm:ss

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1} 'a' 'les' {0}
F(l)_dt={1} 'a' 'les' {0}
F(m)_dt={1}, {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=LLL 'de' y
F_yMMMM=LLLL 'de' y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='setmana' w 'de' Y

I={0} - {1}

# labels of elements
L_era=era
L_year=any
L_quarter=trimestre
L_month=mes
L_week=setmana
L_day=dia
L_weekday=dia de la setmana
L_dayperiod=a. m./p. m.
L_hour=hora
L_minute=minut
L_second=segon
L_zone=fus horari
