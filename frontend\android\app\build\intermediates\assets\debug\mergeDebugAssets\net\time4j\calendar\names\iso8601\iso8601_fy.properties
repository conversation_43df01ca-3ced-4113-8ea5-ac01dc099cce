# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mrt
M(a)_4=Apr
M(a)_5=Mai
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Aug
M(a)_9=Sep
M(a)_10=Okt
M(a)_11=Nov
M(a)_12=Des

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=<PERSON><PERSON><PERSON><PERSON>
M(w)_2=<PERSON><PERSON><PERSON>
M(w)_3=<PERSON><PERSON>
M(w)_4=April
M(w)_5=Maaie
M(w)_6=Juny
M(w)_7=July
M(w)_8=Augustus
M(w)_9=Septimber
M(w)_10=Oktober
M(w)_11=Novimber
M(w)_12=Desimber

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mrt
M(A)_4=Apr
M(A)_5=Mai
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Aug
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Des

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Jannewaris
M(W)_2=Febrewaris
M(W)_3=Maart
M(W)_4=April
M(W)_5=Maaie
M(W)_6=Juny
M(W)_7=July
M(W)_8=Augustus
M(W)_9=Septimber
M(W)_10=Oktober
M(W)_11=Novimber
M(W)_12=Desimber

# weekdays
D(a)_1=mo
D(a)_2=ti
D(a)_3=wo
D(a)_4=to
D(a)_5=fr
D(a)_6=so
D(a)_7=si

D(n)_1=M
D(n)_2=T
D(n)_3=W
D(n)_4=T
D(n)_5=F
D(n)_6=S
D(n)_7=S

D(s)_1=mo
D(s)_2=ti
D(s)_3=wo
D(s)_4=to
D(s)_5=fr
D(s)_6=so
D(s)_7=si

D(w)_1=moandei
D(w)_2=tiisdei
D(w)_3=woansdei
D(w)_4=tongersdei
D(w)_5=freed
D(w)_6=sneon
D(w)_7=snein

D(A)_1=mo
D(A)_2=ti
D(A)_3=wo
D(A)_4=to
D(A)_5=fr
D(A)_6=so
D(A)_7=si

D(N)_1=M
D(N)_2=T
D(N)_3=W
D(N)_4=T
D(N)_5=F
D(N)_6=S
D(N)_7=S

D(S)_1=mo
D(S)_2=ti
D(S)_3=wo
D(S)_4=to
D(S)_5=fr
D(S)_6=so
D(S)_7=si

D(W)_1=moandei
D(W)_2=tiisdei
D(W)_3=woansdei
D(W)_4=tongersdei
D(W)_5=freed
D(W)_6=sneon
D(W)_7=snein

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1e fearnsjier
Q(w)_2=2e fearnsjier
Q(w)_3=3e fearnsjier
Q(w)_4=4e fearnsjier

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1e fearnsjier
Q(W)_2=2e fearnsjier
Q(W)_3=3e fearnsjier
Q(W)_4=4e fearnsjier

# day-period-translations
P(a)_am=AM
P(a)_pm=PM

P(n)_am=AM
P(n)_pm=PM

P(w)_am=AM
P(w)_pm=PM

P(A)_am=AM
P(A)_pm=PM

P(N)_am=AM
P(N)_pm=PM

P(W)_am=AM
P(W)_pm=PM

# eras
E(w)_0=Foar Kristus
E(w|alt)_0=foar gewoane jiertelling
E(w)_1=nei Kristus
E(w|alt)_1=gewoane jiertelling

E(a)_0=f.Kr.
E(a|alt)_0=f.g.j.
E(a)_1=n.Kr.
E(a|alt)_1=g.j.

E(n)_0=f.K.
E(n|alt)_0=fgj
E(n)_1=n.K.
E(n|alt)_1=gj

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd-MM-yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'om' {0}
F(l)_dt={1} 'om' {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d-M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='wike' w 'fan' Y

I={0} - {1}

# labels of elements
L_era=Tiidsrin
L_year=Jier
L_quarter=Fearnsjier
L_month=Moanne
L_week=Wike
L_day=dei
L_weekday=dei van de wike
L_dayperiod=AM/PM
L_hour=oere
L_minute=Minút
L_second=Sekonde
L_zone=Zone
