# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mac
M(a)_4=Apr
M(a)_5=Mei
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Ogo
M(a)_9=Sep
M(a)_10=Okt
M(a)_11=Nov
M(a)_12=Dis

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=O
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=Januari
M(w)_2=Februari
M(w)_3=Mac
M(w)_4=April
M(w)_5=Mei
M(w)_6=Jun
M(w)_7=Julai
M(w)_8=Ogos
M(w)_9=September
M(w)_10=Oktober
M(w)_11=November
M(w)_12=Disember

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mac
M(A)_4=Apr
M(A)_5=Mei
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Ogo
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Dis

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=O
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januari
M(W)_2=Februari
M(W)_3=Mac
M(W)_4=April
M(W)_5=Mei
M(W)_6=Jun
M(W)_7=Julai
M(W)_8=Ogos
M(W)_9=September
M(W)_10=Oktober
M(W)_11=November
M(W)_12=Disember

# weekdays
D(a)_1=Isn
D(a)_2=Sel
D(a)_3=Rab
D(a)_4=Kha
D(a)_5=Jum
D(a)_6=Sab
D(a)_7=Ahd

D(n)_1=I
D(n)_2=S
D(n)_3=R
D(n)_4=K
D(n)_5=J
D(n)_6=S
D(n)_7=A

D(s)_1=Is
D(s)_2=Se
D(s)_3=Ra
D(s)_4=Kh
D(s)_5=Ju
D(s)_6=Sa
D(s)_7=Ah

D(w)_1=Isnin
D(w)_2=Selasa
D(w)_3=Rabu
D(w)_4=Khamis
D(w)_5=Jumaat
D(w)_6=Sabtu
D(w)_7=Ahad

D(A)_1=Isn
D(A)_2=Sel
D(A)_3=Rab
D(A)_4=Kha
D(A)_5=Jum
D(A)_6=Sab
D(A)_7=Ahd

D(N)_1=I
D(N)_2=S
D(N)_3=R
D(N)_4=K
D(N)_5=J
D(N)_6=S
D(N)_7=A

D(S)_1=Is
D(S)_2=Se
D(S)_3=Ra
D(S)_4=Kh
D(S)_5=Ju
D(S)_6=Sa
D(S)_7=Ah

D(W)_1=Isnin
D(W)_2=Selasa
D(W)_3=Rabu
D(W)_4=Khamis
D(W)_5=Jumaat
D(W)_6=Sabtu
D(W)_7=Ahad

# quarters
Q(a)_1=S1
Q(a)_2=S2
Q(a)_3=S3
Q(a)_4=S4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=Suku pertama
Q(w)_2=Suku Ke-2
Q(w)_3=Suku Ke-3
Q(w)_4=Suku Ke-4

Q(A)_1=S1
Q(A)_2=S2
Q(A)_3=S3
Q(A)_4=S4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=Suku pertama
Q(W)_2=Suku Ke-2
Q(W)_3=Suku Ke-3
Q(W)_4=Suku Ke-4

# day-period-rules
T0000=morning1
T0100=morning2
T1200=afternoon1
T1400=evening1
T1900=night1

# day-period-translations
P(a)_am=PG
P(a)_pm=PTG
P(a)_morning1=tengah malam
P(a)_morning2=pagi
P(a)_afternoon1=tengah hari
P(a)_evening1=petang
P(a)_night1=malam

P(n)_am=a
P(n)_pm=p
P(n)_morning1=tengah malam
P(n)_morning2=pagi
P(n)_afternoon1=tengah hari
P(n)_evening1=petang
P(n)_night1=malam

P(w)_am=PG
P(w)_pm=PTG
P(w)_morning1=tengah malam
P(w)_morning2=pagi
P(w)_afternoon1=tengah hari
P(w)_evening1=petang
P(w)_night1=malam

P(A)_am=PG
P(A)_pm=PTG
P(A)_morning1=tengah malam
P(A)_morning2=pagi
P(A)_afternoon1=tengah hari
P(A)_evening1=petang
P(A)_night1=malam

P(N)_am=a
P(N)_pm=p
P(N)_morning1=tengah malam
P(N)_morning2=pagi
P(N)_afternoon1=tengah hari
P(N)_evening1=petang
P(N)_night1=malam

P(W)_am=PG
P(W)_pm=PTG
P(W)_morning1=tengah malam
P(W)_morning2=pagi
P(W)_afternoon1=tengah hari
P(W)_evening1=petang
P(W)_night1=malam

# eras
E(w)_0=S.M.
E(w|alt)_0=BCE
E(w)_1=TM
E(w|alt)_1=CE

E(a)_0=S.M.
E(a|alt)_0=BCE
E(a)_1=TM
E(a|alt)_1=CE

E(n)_0=S.M.
E(n)_1=TM

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=d/MM/yy

F(alt)=HH:mm:ss

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d-M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y

I={0} – {1}

# labels of elements
L_era=era
L_year=tahun
L_quarter=suku tahun
L_month=bulan
L_week=minggu
L_day=hari
L_weekday=Hari dalam Minggu
L_dayperiod=PG/PTG
L_hour=jam
L_minute=minit
L_second=saat
L_zone=zon waktu
