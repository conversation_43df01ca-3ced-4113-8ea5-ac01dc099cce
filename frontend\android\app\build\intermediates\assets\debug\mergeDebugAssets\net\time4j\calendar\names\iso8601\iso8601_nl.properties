# months
M(a)_1=jan.
M(a)_2=feb.
M(a)_3=mrt.
M(a)_4=apr.
M(a)_5=mei
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=aug.
M(a)_9=sep.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=januari
M(w)_2=februari
M(w)_3=maart
M(w)_4=april
M(w)_5=mei
M(w)_6=juni
M(w)_7=juli
M(w)_8=augustus
M(w)_9=september
M(w)_10=oktober
M(w)_11=november
M(w)_12=december

M(A)_1=jan.
M(A)_2=feb.
M(A)_3=mrt.
M(A)_4=apr.
M(A)_5=mei
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=aug.
M(A)_9=sep.
M(A)_10=okt.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=januari
M(W)_2=februari
M(W)_3=maart
M(W)_4=april
M(W)_5=mei
M(W)_6=juni
M(W)_7=juli
M(W)_8=augustus
M(W)_9=september
M(W)_10=oktober
M(W)_11=november
M(W)_12=december

# weekdays
D(a)_1=ma
D(a)_2=di
D(a)_3=wo
D(a)_4=do
D(a)_5=vr
D(a)_6=za
D(a)_7=zo

D(n)_1=M
D(n)_2=D
D(n)_3=W
D(n)_4=D
D(n)_5=V
D(n)_6=Z
D(n)_7=Z

D(s)_1=ma
D(s)_2=di
D(s)_3=wo
D(s)_4=do
D(s)_5=vr
D(s)_6=za
D(s)_7=zo

D(w)_1=maandag
D(w)_2=dinsdag
D(w)_3=woensdag
D(w)_4=donderdag
D(w)_5=vrijdag
D(w)_6=zaterdag
D(w)_7=zondag

D(A)_1=ma
D(A)_2=di
D(A)_3=wo
D(A)_4=do
D(A)_5=vr
D(A)_6=za
D(A)_7=zo

D(N)_1=M
D(N)_2=D
D(N)_3=W
D(N)_4=D
D(N)_5=V
D(N)_6=Z
D(N)_7=Z

D(S)_1=ma
D(S)_2=di
D(S)_3=wo
D(S)_4=do
D(S)_5=vr
D(S)_6=za
D(S)_7=zo

D(W)_1=maandag
D(W)_2=dinsdag
D(W)_3=woensdag
D(W)_4=donderdag
D(W)_5=vrijdag
D(W)_6=zaterdag
D(W)_7=zondag

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1e kwartaal
Q(w)_2=2e kwartaal
Q(w)_3=3e kwartaal
Q(w)_4=4e kwartaal

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1e kwartaal
Q(W)_2=2e kwartaal
Q(W)_3=3e kwartaal
Q(W)_4=4e kwartaal

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=middernacht
P(a)_am=a.m.
P(a)_pm=p.m.
P(a)_morning1=’s ochtends
P(a)_afternoon1=’s middags
P(a)_evening1=’s avonds
P(a)_night1=’s nachts

P(n)_midnight=middernacht
P(n)_am=a.m.
P(n)_pm=p.m.
P(n)_morning1=’s ochtends
P(n)_afternoon1=’s middags
P(n)_evening1=’s avonds
P(n)_night1=’s nachts

P(w)_midnight=middernacht
P(w)_am=a.m.
P(w)_pm=p.m.
P(w)_morning1=’s ochtends
P(w)_afternoon1=’s middags
P(w)_evening1=’s avonds
P(w)_night1=’s nachts

P(A)_midnight=middernacht
P(A)_am=a.m.
P(A)_pm=p.m.
P(A)_morning1=ochtend
P(A)_afternoon1=middag
P(A)_evening1=avond
P(A)_night1=nacht

P(N)_midnight=middernacht
P(N)_am=a.m.
P(N)_pm=p.m.
P(N)_morning1=ochtend
P(N)_afternoon1=middag
P(N)_evening1=avond
P(N)_night1=nacht

P(W)_midnight=middernacht
P(W)_am=a.m.
P(W)_pm=p.m.
P(W)_morning1=ochtend
P(W)_afternoon1=middag
P(W)_evening1=avond
P(W)_night1=nacht

# eras
E(w)_0=voor Christus
E(w|alt)_0=vóór gewone jaartelling
E(w)_1=na Christus
E(w|alt)_1=gewone jaartelling

E(a)_0=v.Chr.
E(a|alt)_0=v.g.j.
E(a)_1=n.Chr.
E(a|alt)_1=g.j.

E(n)_0=v.C.
E(n|alt)_0=vgj
E(n)_1=n.C.
E(n|alt)_1=gj

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd-MM-y

F(alt)=H:mm:ss' uur'

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'om' {0}
F(l)_dt={1} 'om' {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d-M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='week' w 'in' Y

I={0} - {1}

# labels of elements
L_era=tijdperk
L_year=jaar
L_quarter=kwartaal
L_month=maand
L_week=week
L_day=dag
L_weekday=dag van de week
L_dayperiod=a.m./p.m.
L_hour=uur
L_minute=minuut
L_second=seconde
L_zone=tijdzone
