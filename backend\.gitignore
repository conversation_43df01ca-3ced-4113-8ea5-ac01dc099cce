# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# dotenv environment variables
.env
.env.*
!.env.example

# Logs
logs/
*.log
*.out
*.err

# OS generated files
.DS_Store
Thumbs.db

# Build output
dist/
build/
tmp/
temp/
.cache/
.next/
out/
coverage/
lib/
*.tsbuildinfo

# Database
*.sqlite
*.sqlite3
*.db
*.db3
*.s3db
*.sdb
*.db-journal

# VSCode
.vscode/

# Test coverage
coverage/
.nyc_output/

# PM2 logs
pids
*.pid
*.seed
*.pid.lock

# Misc
.idea/
*.swp
*.swo

# TypeScript
*.tsbuildinfo

# Prisma
prisma/dev.db
prisma/dev.db-journal

# Local secrets
secrets/
secret/
*.key
*.pem

# Python virtualenv (if any)
venv/
ENV/
env/
.venv/

# Mac system files
.AppleDouble
.LSOverride

# Windows system files
ehthumbs.db
Desktop.ini

# Docker
docker-compose.override.yml

# Ignore backend uploads/media if present
uploads/
media/
