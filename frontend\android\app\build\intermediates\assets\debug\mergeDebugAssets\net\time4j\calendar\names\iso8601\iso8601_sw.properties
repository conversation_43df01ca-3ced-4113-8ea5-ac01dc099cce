# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mac
M(a)_4=Apr
M(a)_5=Mei
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Ago
M(a)_9=Sep
M(a)_10=Okt
M(a)_11=Nov
M(a)_12=Des

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=Jan<PERSON>ri
M(w)_2=Februari
M(w)_3=Machi
M(w)_4=Aprili
M(w)_5=Mei
M(w)_6=Juni
M(w)_7=Julai
M(w)_8=Agosti
M(w)_9=Septemba
M(w)_10=Oktoba
M(w)_11=Novemba
M(w)_12=Desemba

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mac
M(A)_4=Apr
M(A)_5=Mei
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Ago
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Des

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januari
M(W)_2=Februari
M(W)_3=Machi
M(W)_4=Aprili
M(W)_5=Mei
M(W)_6=Juni
M(W)_7=Julai
M(W)_8=Agosti
M(W)_9=Septemba
M(W)_10=Oktoba
M(W)_11=Novemba
M(W)_12=Desemba

# weekdays
D(a)_1=Jumatatu
D(a)_2=Jumanne
D(a)_3=Jumatano
D(a)_4=Alhamisi
D(a)_5=Ijumaa
D(a)_6=Jumamosi
D(a)_7=Jumapili

D(n)_1=M
D(n)_2=T
D(n)_3=W
D(n)_4=T
D(n)_5=F
D(n)_6=S
D(n)_7=S

D(s)_1=Jumatatu
D(s)_2=Jumanne
D(s)_3=Jumatano
D(s)_4=Alhamisi
D(s)_5=Ijumaa
D(s)_6=Jumamosi
D(s)_7=Jumapili

D(w)_1=Jumatatu
D(w)_2=Jumanne
D(w)_3=Jumatano
D(w)_4=Alhamisi
D(w)_5=Ijumaa
D(w)_6=Jumamosi
D(w)_7=Jumapili

D(A)_1=Jumatatu
D(A)_2=Jumanne
D(A)_3=Jumatano
D(A)_4=Alhamisi
D(A)_5=Ijumaa
D(A)_6=Jumamosi
D(A)_7=Jumapili

D(N)_1=M
D(N)_2=T
D(N)_3=W
D(N)_4=T
D(N)_5=F
D(N)_6=S
D(N)_7=S

D(S)_1=Jumatatu
D(S)_2=Jumanne
D(S)_3=Jumatano
D(S)_4=Alhamisi
D(S)_5=Ijumaa
D(S)_6=Jumamosi
D(S)_7=Jumapili

D(W)_1=Jumatatu
D(W)_2=Jumanne
D(W)_3=Jumatano
D(W)_4=Alhamisi
D(W)_5=Ijumaa
D(W)_6=Jumamosi
D(W)_7=Jumapili

# quarters
Q(a)_1=Robo ya 1
Q(a)_2=Robo ya 2
Q(a)_3=Robo ya 3
Q(a)_4=Robo ya 4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=Robo ya 1
Q(w)_2=Robo ya 2
Q(w)_3=Robo ya 3
Q(w)_4=Robo ya 4

Q(A)_1=Robo ya 1
Q(A)_2=Robo ya 2
Q(A)_3=Robo ya 3
Q(A)_4=Robo ya 4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=Robo ya 1
Q(W)_2=Robo ya 2
Q(W)_3=Robo ya 3
Q(W)_4=Robo ya 4

# day-period-rules
T0400=morning1
T0700=morning2
T1200=afternoon1
T1600=evening1
T1900=night1

# day-period-translations
P(a)_midnight=saa sita za usiku
P(a)_am=AM
P(a)_noon=saa sita za mchana
P(a)_pm=PM
P(a)_morning1=alfajiri
P(a)_morning2=asubuhi
P(a)_afternoon1=mchana
P(a)_evening1=jioni
P(a)_night1=usiku

P(n)_midnight=usiku
P(n)_am=am
P(n)_noon=mchana
P(n)_pm=pm
P(n)_morning1=alfajiri
P(n)_morning2=asubuhi
P(n)_afternoon1=mchana
P(n)_evening1=jioni
P(n)_night1=usiku

P(w)_midnight=saa sita za usiku
P(w)_am=AM
P(w)_noon=saa sita za mchana
P(w)_pm=PM
P(w)_morning1=alfajiri
P(w)_morning2=asubuhi
P(w)_afternoon1=mchana
P(w)_evening1=jioni
P(w)_night1=usiku

P(A)_midnight=saa sita za usiku
P(A)_am=AM
P(A)_noon=saa sita za mchana
P(A)_pm=PM
P(A)_morning1=alfajiri
P(A)_morning2=asubuhi
P(A)_afternoon1=alasiri
P(A)_evening1=jioni
P(A)_night1=usiku

P(N)_midnight=saa sita za usiku
P(N)_am=AM
P(N)_noon=saa sita za mchana
P(N)_pm=PM
P(N)_morning1=alfajiri
P(N)_morning2=asubuhi
P(N)_afternoon1=mchana
P(N)_evening1=jioni
P(N)_night1=usiku

P(W)_midnight=saa sita za usiku
P(W)_am=AM
P(W)_noon=saa sita za mchana
P(W)_pm=PM
P(W)_morning1=alfajiri
P(W)_morning2=asubuhi
P(W)_afternoon1=mchana
P(W)_evening1=jioni
P(W)_night1=usiku

# eras
E(w)_0=Kabla ya Kristo
E(w|alt)_0=BCE
E(w)_1=Baada ya Kristo
E(w|alt)_1=CE

E(a)_0=KK
E(a|alt)_0=BCE
E(a)_1=BK
E(a|alt)_1=CE

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/y

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=y QQQ
F_yQQQQ=QQQQ y
F_yw='wiki' w 'ya' Y

I={0} – {1}

# labels of elements
L_era=enzi
L_year=mwaka
L_quarter=robo
L_month=mwezi
L_week=wiki
L_day=siku
L_weekday=siku ya wiki
L_dayperiod=AM/PM
L_hour=saa
L_minute=dakika
L_second=sekunde
L_zone=saa za eneo
