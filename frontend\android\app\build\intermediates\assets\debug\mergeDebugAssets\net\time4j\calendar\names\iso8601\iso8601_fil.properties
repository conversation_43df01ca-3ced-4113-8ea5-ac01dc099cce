# months
M(a)_1=Ene
M(a)_2=Peb
M(a)_3=Mar
M(a)_4=Abr
M(a)_5=May
M(a)_6=Hun
M(a)_7=Hul
M(a)_8=Ago
M(a)_9=Set
M(a)_10=Okt
M(a)_11=Nob
M(a)_12=Dis

M(n)_1=Ene
M(n)_2=Peb
M(n)_3=Mar
M(n)_4=Abr
M(n)_5=May
M(n)_6=Hun
M(n)_7=Hul
M(n)_8=Ago
M(n)_9=Set
M(n)_10=Okt
M(n)_11=Nob
M(n)_12=Dis

M(w)_1=Enero
M(w)_2=Pebrero
M(w)_3=Marso
M(w)_4=Abril
M(w)_5=Mayo
M(w)_6=Hunyo
M(w)_7=Hulyo
M(w)_8=Agosto
M(w)_9=Setyembre
M(w)_10=Oktubre
M(w)_11=Nobyembre
M(w)_12=Disyembre

M(A)_1=Ene
M(A)_2=Peb
M(A)_3=Mar
M(A)_4=Abr
M(A)_5=May
M(A)_6=Hun
M(A)_7=Hul
M(A)_8=Ago
M(A)_9=Set
M(A)_10=Okt
M(A)_11=Nob
M(A)_12=Dis

M(N)_1=E
M(N)_2=P
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=Hun
M(N)_7=Hul
M(N)_8=Ago
M(N)_9=Set
M(N)_10=Okt
M(N)_11=Nob
M(N)_12=Dis

M(W)_1=Enero
M(W)_2=Pebrero
M(W)_3=Marso
M(W)_4=Abril
M(W)_5=Mayo
M(W)_6=Hunyo
M(W)_7=Hulyo
M(W)_8=Agosto
M(W)_9=Setyembre
M(W)_10=Oktubre
M(W)_11=Nobyembre
M(W)_12=Disyembre

# weekdays
D(a)_1=Lun
D(a)_2=Mar
D(a)_3=Miy
D(a)_4=Huw
D(a)_5=Biy
D(a)_6=Sab
D(a)_7=Lin

D(n)_1=Lun
D(n)_2=Mar
D(n)_3=Miy
D(n)_4=Huw
D(n)_5=Biy
D(n)_6=Sab
D(n)_7=Lin

D(s)_1=Lu
D(s)_2=Ma
D(s)_3=Mi
D(s)_4=Hu
D(s)_5=Bi
D(s)_6=Sa
D(s)_7=Li

D(w)_1=Lunes
D(w)_2=Martes
D(w)_3=Miyerkules
D(w)_4=Huwebes
D(w)_5=Biyernes
D(w)_6=Sabado
D(w)_7=Linggo

D(A)_1=Lun
D(A)_2=Mar
D(A)_3=Miy
D(A)_4=Huw
D(A)_5=Biy
D(A)_6=Sab
D(A)_7=Lin

D(N)_1=Lun
D(N)_2=Mar
D(N)_3=Miy
D(N)_4=Huw
D(N)_5=Biy
D(N)_6=Sab
D(N)_7=Lin

D(S)_1=Lu
D(S)_2=Ma
D(S)_3=Mi
D(S)_4=Hu
D(S)_5=Bi
D(S)_6=Sa
D(S)_7=Li

D(W)_1=Lunes
D(W)_2=Martes
D(W)_3=Miyerkules
D(W)_4=Huwebes
D(W)_5=Biyernes
D(W)_6=Sabado
D(W)_7=Linggo

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=ika-1 quarter
Q(w)_2=ika-2 quarter
Q(w)_3=ika-3 quarter
Q(w)_4=ika-4 na quarter

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=ika-1 quarter
Q(W)_2=ika-2 quarter
Q(W)_3=ika-3 quarter
Q(W)_4=ika-4 na quarter

# day-period-rules
T0000=morning1
T0600=morning2
T1200=afternoon1
T1600=evening1
T1800=night1

# day-period-translations
P(a)_midnight=hatinggabi
P(a)_am=AM
P(a)_noon=tanghaling-tapat
P(a)_pm=PM
P(a)_morning1=nang umaga
P(a)_morning2=madaling-araw
P(a)_afternoon1=tanghali
P(a)_evening1=ng hapon
P(a)_night1=gabi

P(n)_midnight=hatinggabi
P(n)_am=am
P(n)_noon=tanghaling-tapat
P(n)_pm=pm
P(n)_morning1=umaga
P(n)_morning2=madaling-araw
P(n)_afternoon1=sa hapon
P(n)_evening1=sa gabi
P(n)_night1=gabi

P(w)_midnight=hatinggabi
P(w)_am=AM
P(w)_noon=tanghaling-tapat
P(w)_pm=PM
P(w)_morning1=nang umaga
P(w)_morning2=madaling-araw
P(w)_afternoon1=tanghali
P(w)_evening1=ng hapon
P(w)_night1=ng gabi

P(A)_midnight=hatinggabi
P(A)_am=AM
P(A)_noon=tanghaling-tapat
P(A)_pm=PM
P(A)_morning1=umaga
P(A)_morning2=madaling-araw
P(A)_afternoon1=tanghali
P(A)_evening1=hapon
P(A)_night1=gabi

P(N)_midnight=hatinggabi
P(N)_am=AM
P(N)_noon=tanghaling-tapat
P(N)_pm=PM
P(N)_morning1=umaga
P(N)_morning2=madaling-araw
P(N)_afternoon1=tanghali
P(N)_evening1=gabi
P(N)_night1=gabi

P(W)_midnight=hatinggabi
P(W)_am=AM
P(W)_noon=tanghaling-tapat
P(W)_pm=PM
P(W)_morning1=umaga
P(W)_morning2=madaling-araw
P(W)_afternoon1=tanghali
P(W)_evening1=hapon
P(W)_night1=gabi

# eras
E(w)_0=Before Christ
E(w|alt)_0=Before Common Era
E(w)_1=Anno Domini
E(w|alt)_1=Common Era

E(a)_0=BC
E(a|alt)_0=BCE
E(a)_1=AD
E(a|alt)_1=CE

# format patterns
F(f)_d=EEEE, MMMM d, y
F(l)_d=MMMM d, y
F(m)_d=MMM d, y
F(s)_d=M/d/yy

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} 'nang' {0}
F(l)_dt={1} 'nang' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=M/d
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=M/y
F_yMM=MM/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw_one='ika'-w 'linggo' 'ng' Y
F_yw='linggo' w 'ng' Y

I={0} – {1}

# labels of elements
L_era=panahon
L_year=taon
L_quarter=Quarter
L_month=buwan
L_week=linggo
L_day=araw
L_weekday=araw ng linggo
L_dayperiod=AM/PM
L_hour=oras
L_minute=minuto
L_second=segundo
L_zone=time zone
