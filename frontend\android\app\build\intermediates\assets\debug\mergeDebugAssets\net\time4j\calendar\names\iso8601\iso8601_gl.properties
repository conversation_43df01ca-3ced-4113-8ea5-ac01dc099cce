# months
M(a)_1=xan.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=abr.
M(a)_5=maio
M(a)_6=xuño
M(a)_7=xul.
M(a)_8=ago.
M(a)_9=set.
M(a)_10=out.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=x.
M(n)_2=f.
M(n)_3=m.
M(n)_4=a.
M(n)_5=m.
M(n)_6=x.
M(n)_7=x.
M(n)_8=a.
M(n)_9=s.
M(n)_10=o.
M(n)_11=n.
M(n)_12=d.

M(w)_1=xaneiro
M(w)_2=febreiro
M(w)_3=marzo
M(w)_4=abril
M(w)_5=maio
M(w)_6=xuño
M(w)_7=xullo
M(w)_8=agosto
M(w)_9=setembro
M(w)_10=outubro
M(w)_11=novembro
M(w)_12=decembro

M(A)_1=Xan.
M(A)_2=Feb.
M(A)_3=Mar.
M(A)_4=Abr.
M(A)_5=Maio
M(A)_6=Xuño
M(A)_7=Xul.
M(A)_8=Ago.
M(A)_9=Set.
M(A)_10=Out.
M(A)_11=Nov.
M(A)_12=Dec.

M(N)_1=X
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=X
M(N)_7=X
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Xaneiro
M(W)_2=Febreiro
M(W)_3=Marzo
M(W)_4=Abril
M(W)_5=Maio
M(W)_6=Xuño
M(W)_7=Xullo
M(W)_8=Agosto
M(W)_9=Setembro
M(W)_10=Outubro
M(W)_11=Novembro
M(W)_12=Decembro

# weekdays
D(a)_1=luns
D(a)_2=mar.
D(a)_3=mér.
D(a)_4=xov.
D(a)_5=ven.
D(a)_6=sáb.
D(a)_7=dom.

D(n)_1=l.
D(n)_2=m.
D(n)_3=m.
D(n)_4=x.
D(n)_5=v.
D(n)_6=s.
D(n)_7=d.

D(s)_1=lu.
D(s)_2=ma.
D(s)_3=mé.
D(s)_4=xo.
D(s)_5=ve.
D(s)_6=sá.
D(s)_7=do.

D(w)_1=luns
D(w)_2=martes
D(w)_3=mércores
D(w)_4=xoves
D(w)_5=venres
D(w)_6=sábado
D(w)_7=domingo

D(A)_1=Luns
D(A)_2=Mar.
D(A)_3=Mér.
D(A)_4=Xov.
D(A)_5=Ven.
D(A)_6=Sáb.
D(A)_7=Dom.

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=X
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=Lu
D(S)_2=Ma
D(S)_3=Mé
D(S)_4=Xo
D(S)_5=Ve
D(S)_6=Sá
D(S)_7=Do

D(W)_1=Luns
D(W)_2=Martes
D(W)_3=Mércores
D(W)_4=Xoves
D(W)_5=Venres
D(W)_6=Sábado
D(W)_7=Domingo

# quarters
Q(a)_1=T1
Q(a)_2=T2
Q(a)_3=T3
Q(a)_4=T4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1.º trimestre
Q(w)_2=2.º trimestre
Q(w)_3=3.º trimestre
Q(w)_4=4.º trimestre

Q(A)_1=T1
Q(A)_2=T2
Q(A)_3=T3
Q(A)_4=T4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1.º trimestre
Q(W)_2=2.º trimestre
Q(W)_3=3.º trimestre
Q(W)_4=4.º trimestre

# day-period-rules
T0000=morning1
T0600=morning2
T1200=afternoon1
T1300=evening1
T2100=night1

# day-period-translations
P(a)_midnight=da noite
P(a)_am=a.m.
P(a)_pm=p.m.
P(a)_morning1=da madrugada
P(a)_morning2=da mañá
P(a)_afternoon1=do mediodía
P(a)_evening1=da tarde
P(a)_night1=da noite

P(n)_midnight=da noite
P(n)_am=a.m.
P(n)_pm=p.m.
P(n)_morning1=da madrugada
P(n)_morning2=da mañá
P(n)_afternoon1=do mediodía
P(n)_evening1=da tarde
P(n)_night1=da noite

P(w)_midnight=da noite
P(w)_am=a.m.
P(w)_pm=p.m.
P(w)_morning1=da madrugada
P(w)_morning2=da mañá
P(w)_afternoon1=do mediodía
P(w)_evening1=da tarde
P(w)_night1=da noite

P(A)_midnight=medianoite
P(A)_am=a.m.
P(A)_pm=p.m.
P(A)_morning1=madrugada
P(A)_morning2=mañá
P(A)_afternoon1=mediodía
P(A)_evening1=tarde
P(A)_night1=noite

P(N)_midnight=medianoite
P(N)_am=a.m.
P(N)_pm=p.m.
P(N)_morning1=madrugada
P(N)_morning2=mañá
P(N)_afternoon1=mediodía
P(N)_evening1=tarde
P(N)_night1=noite

P(W)_midnight=medianoite
P(W)_am=a.m.
P(W)_pm=p.m.
P(W)_morning1=madrugada
P(W)_morning2=mañá
P(W)_afternoon1=mediodía
P(W)_evening1=tarde
P(W)_night1=noite

# eras
E(w)_0=antes de Cristo
E(w|alt)_0=antes da era común
E(w)_1=despois de Cristo
E(w|alt)_1=da era común

E(a)_0=a.C.
E(a|alt)_0=a.e.c.
E(a)_1=d.C.
E(a|alt)_1=e.c.

# format patterns
F(f)_d=EEEE, d 'de' MMMM 'de' y
F(l)_d=d 'de' MMMM 'de' y
F(m)_d=d 'de' MMM 'de' y
F(s)_d=dd/MM/yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={0} 'do' {1}
F(l)_dt={0} 'do' {1}
F(m)_dt={0}, {1}
F(s)_dt={0}, {1}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d 'de' MMM
F_MMMMd=d 'de' MMMM
F_y=y
F_yM=M/y
F_yMM=MM/y
F_yMMM=MMM 'de' y
F_yMMMM=MMMM 'de' y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ 'de' y
F_yw=w.'ª' 'semana' 'de' Y

I={0} – {1}

# labels of elements
L_era=era
L_year=ano
L_quarter=trimestre
L_month=mes
L_week=semana
L_day=día
L_weekday=día da semana
L_dayperiod=a.m./p.m.
L_hour=hora
L_minute=minuto
L_second=segundo
L_zone=fuso horario
