# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mar
M(a)_4=Apr
M(a)_5=Mei
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Agu
M(a)_9=Sep
M(a)_10=Okt
M(a)_11=Nov
M(a)_12=Des

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=Januari
M(w)_2=Februari
M(w)_3=Maret
M(w)_4=April
M(w)_5=Mei
M(w)_6=Juni
M(w)_7=Juli
M(w)_8=Agustus
M(w)_9=September
M(w)_10=Oktober
M(w)_11=November
M(w)_12=Desember

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mar
M(A)_4=Apr
M(A)_5=Mei
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Agu
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Des

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januari
M(W)_2=Februari
M(W)_3=Maret
M(W)_4=April
M(W)_5=Mei
M(W)_6=Juni
M(W)_7=Juli
M(W)_8=Agustus
M(W)_9=September
M(W)_10=Oktober
M(W)_11=November
M(W)_12=Desember

# weekdays
D(a)_1=Sen
D(a)_2=Sel
D(a)_3=Rab
D(a)_4=Kam
D(a)_5=Jum
D(a)_6=Sab
D(a)_7=Min

D(n)_1=S
D(n)_2=S
D(n)_3=R
D(n)_4=K
D(n)_5=J
D(n)_6=S
D(n)_7=M

D(s)_1=Sen
D(s)_2=Sel
D(s)_3=Rab
D(s)_4=Kam
D(s)_5=Jum
D(s)_6=Sab
D(s)_7=Min

D(w)_1=Senin
D(w)_2=Selasa
D(w)_3=Rabu
D(w)_4=Kamis
D(w)_5=Jumat
D(w)_6=Sabtu
D(w)_7=Minggu

D(A)_1=Sen
D(A)_2=Sel
D(A)_3=Rab
D(A)_4=Kam
D(A)_5=Jum
D(A)_6=Sab
D(A)_7=Min

D(N)_1=S
D(N)_2=S
D(N)_3=R
D(N)_4=K
D(N)_5=J
D(N)_6=S
D(N)_7=M

D(S)_1=Sen
D(S)_2=Sel
D(S)_3=Rab
D(S)_4=Kam
D(S)_5=Jum
D(S)_6=Sab
D(S)_7=Min

D(W)_1=Senin
D(W)_2=Selasa
D(W)_3=Rabu
D(W)_4=Kamis
D(W)_5=Jumat
D(W)_6=Sabtu
D(W)_7=Minggu

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=Kuartal ke-1
Q(w)_2=Kuartal ke-2
Q(w)_3=Kuartal ke-3
Q(w)_4=Kuartal ke-4

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=Kuartal ke-1
Q(W)_2=Kuartal ke-2
Q(W)_3=Kuartal ke-3
Q(W)_4=Kuartal ke-4

# day-period-rules
T0000=morning1
T1000=afternoon1
T1500=evening1
T1800=night1

# day-period-translations
P(a)_midnight=tengah malam
P(a)_am=AM
P(a)_noon=tengah hari
P(a)_pm=PM
P(a)_morning1=pagi
P(a)_afternoon1=siang
P(a)_evening1=sore
P(a)_night1=malam

P(n)_midnight=tengah malam
P(n)_am=AM
P(n)_noon=tengah hari
P(n)_pm=PM
P(n)_morning1=pagi
P(n)_afternoon1=siang
P(n)_evening1=sore
P(n)_night1=malam

P(w)_midnight=tengah malam
P(w)_am=AM
P(w)_noon=tengah hari
P(w)_pm=PM
P(w)_morning1=pagi
P(w)_afternoon1=siang
P(w)_evening1=sore
P(w)_night1=malam

P(A)_midnight=tengah malam
P(A)_am=AM
P(A)_noon=tengah hari
P(A)_pm=PM
P(A)_morning1=pagi
P(A)_afternoon1=siang
P(A)_evening1=sore
P(A)_night1=malam

P(N)_midnight=tengah malam
P(N)_am=AM
P(N)_noon=tengah hari
P(N)_pm=PM
P(N)_morning1=pagi
P(N)_afternoon1=siang
P(N)_evening1=sore
P(N)_night1=malam

P(W)_midnight=tengah malam
P(W)_am=AM
P(W)_noon=tengah hari
P(W)_pm=PM
P(W)_morning1=pagi
P(W)_afternoon1=siang
P(W)_evening1=sore
P(W)_night1=malam

# eras
E(w)_0=Sebelum Masehi
E(w|alt)_0=Sebelum Era Umum
E(w)_1=Masehi
E(w|alt)_1=Era Umum

E(a)_0=SM
E(a|alt)_0=SEU
E(a)_1=M
E(a|alt)_1=EU

E(n)_0=SM
E(n)_1=M

# format patterns
F(f)_d=EEEE, dd MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/yy

F(alt)=HH:mm:ss

F(f)_t=HH.mm.ss zzzz
F(l)_t=HH.mm.ss z
F(m)_t=HH.mm.ss
F(s)_t=HH.mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h.mm B
F_Bhms=h.mm.ss B
F_h=h a
F_H=HH
F_hm=h.mm a
F_Hm=HH.mm
F_hms=h.mm.ss a
F_Hms=HH.mm.ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='minggu' 'ke'-w Y

I={0} – {1}

# labels of elements
L_era=era
L_year=tahun
L_quarter=kuartal
L_month=bulan
L_week=minggu
L_day=hari
L_weekday=hari dalam seminggu
L_dayperiod=AM/PM
L_hour=Jam
L_minute=menit
L_second=detik
L_zone=zona waktu
