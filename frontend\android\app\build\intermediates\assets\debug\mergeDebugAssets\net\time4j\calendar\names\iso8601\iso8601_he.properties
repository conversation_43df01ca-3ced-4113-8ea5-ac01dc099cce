# months
M(a)_1=ינו׳
M(a)_2=פבר׳
M(a)_3=מרץ
M(a)_4=אפר׳
M(a)_5=מאי
M(a)_6=יוני
M(a)_7=יולי
M(a)_8=אוג׳
M(a)_9=ספט׳
M(a)_10=אוק׳
M(a)_11=נוב׳
M(a)_12=דצמ׳

M(w)_1=ינואר
M(w)_2=פברואר
M(w)_3=מרץ
M(w)_4=אפריל
M(w)_5=מאי
M(w)_6=יוני
M(w)_7=יולי
M(w)_8=אוגוסט
M(w)_9=ספטמבר
M(w)_10=אוקטובר
M(w)_11=נובמבר
M(w)_12=דצמבר

M(A)_1=ינו׳
M(A)_2=פבר׳
M(A)_3=מרץ
M(A)_4=אפר׳
M(A)_5=מאי
M(A)_6=יוני
M(A)_7=יולי
M(A)_8=אוג׳
M(A)_9=ספט׳
M(A)_10=אוק׳
M(A)_11=נוב׳
M(A)_12=דצמ׳

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

M(W)_1=ינואר
M(W)_2=פברואר
M(W)_3=מרץ
M(W)_4=אפריל
M(W)_5=מאי
M(W)_6=יוני
M(W)_7=יולי
M(W)_8=אוגוסט
M(W)_9=ספטמבר
M(W)_10=אוקטובר
M(W)_11=נובמבר
M(W)_12=דצמבר

# weekdays
D(a)_1=יום ב׳
D(a)_2=יום ג׳
D(a)_3=יום ד׳
D(a)_4=יום ה׳
D(a)_5=יום ו׳
D(a)_6=שבת
D(a)_7=יום א׳

D(n)_1=ב׳
D(n)_2=ג׳
D(n)_3=ד׳
D(n)_4=ה׳
D(n)_5=ו׳
D(n)_6=ש׳
D(n)_7=א׳

D(s)_1=ב׳
D(s)_2=ג׳
D(s)_3=ד׳
D(s)_4=ה׳
D(s)_5=ו׳
D(s)_6=ש׳
D(s)_7=א׳

D(w)_1=יום שני
D(w)_2=יום שלישי
D(w)_3=יום רביעי
D(w)_4=יום חמישי
D(w)_5=יום שישי
D(w)_6=יום שבת
D(w)_7=יום ראשון

D(A)_1=יום ב׳
D(A)_2=יום ג׳
D(A)_3=יום ד׳
D(A)_4=יום ה׳
D(A)_5=יום ו׳
D(A)_6=שבת
D(A)_7=יום א׳

D(N)_1=ב׳
D(N)_2=ג׳
D(N)_3=ד׳
D(N)_4=ה׳
D(N)_5=ו׳
D(N)_6=ש׳
D(N)_7=א׳

D(S)_1=ב׳
D(S)_2=ג׳
D(S)_3=ד׳
D(S)_4=ה׳
D(S)_5=ו׳
D(S)_6=ש׳
D(S)_7=א׳

D(W)_1=יום שני
D(W)_2=יום שלישי
D(W)_3=יום רביעי
D(W)_4=יום חמישי
D(W)_5=יום שישי
D(W)_6=יום שבת
D(W)_7=יום ראשון

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=רבעון 1
Q(w)_2=רבעון 2
Q(w)_3=רבעון 3
Q(w)_4=רבעון 4

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=רבעון 1
Q(W)_2=רבעון 2
Q(W)_3=רבעון 3
Q(W)_4=רבעון 4

# day-period-rules
T0300=night2
T0600=morning1
T1200=afternoon1
T1600=afternoon2
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=חצות
P(a)_am=לפנה״צ
P(a)_pm=אחה״צ
P(a)_morning1=בוקר
P(a)_afternoon1=צהריים
P(a)_afternoon2=אחר הצהריים
P(a)_evening1=ערב
P(a)_night1=לילה
P(a)_night2=לפנות בוקר

P(n)_midnight=חצות
P(n)_am=לפנה״צ
P(n)_pm=אחה״צ
P(n)_morning1=בבוקר
P(n)_afternoon1=בצהריים
P(n)_afternoon2=אחה״צ
P(n)_evening1=בערב
P(n)_night1=בלילה
P(n)_night2=לפנות בוקר

P(w)_midnight=חצות
P(w)_am=לפנה״צ
P(w)_pm=אחה״צ
P(w)_morning1=בבוקר
P(w)_afternoon1=בצהריים
P(w)_afternoon2=אחר הצהריים
P(w)_evening1=בערב
P(w)_night1=בלילה
P(w)_night2=לפנות בוקר

P(A)_midnight=חצות
P(A)_am=AM
P(A)_pm=PM
P(A)_morning1=בוקר
P(A)_afternoon1=צהריים
P(A)_afternoon2=אחה״צ
P(A)_evening1=ערב
P(A)_night1=לילה
P(A)_night2=לפנות בוקר

P(N)_midnight=חצות
P(N)_am=לפנה״צ
P(N)_pm=אחה״צ
P(N)_morning1=בוקר
P(N)_afternoon1=צהריים
P(N)_afternoon2=אחה״צ
P(N)_evening1=ערב
P(N)_night1=לילה
P(N)_night2=לפנות בוקר

P(W)_midnight=חצות
P(W)_am=AM
P(W)_pm=PM
P(W)_morning1=בוקר
P(W)_afternoon1=צהריים
P(W)_afternoon2=אחר הצהריים
P(W)_evening1=ערב
P(W)_night1=לילה
P(W)_night2=לפנות בוקר

# eras
E(w)_0=לפני הספירה
E(w|alt)_0=לפנה״ס
E(w)_1=לספירה
E(w|alt)_1=CE

E(a)_0=לפנה״ס
E(a|alt)_0=BCE
E(a)_1=לספירה
E(a|alt)_1=CE

# format patterns
F(f)_d=EEEE, d בMMMM y
F(l)_d=d בMMMM y
F(m)_d=d בMMM y
F(s)_d=d.M.y

F(alt)=HH:mm:ss

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1} בשעה {0}
F(l)_dt={1} בשעה {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=‏h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=d.M
F_MMMd=d בMMM
F_MMMMd=d בMMMM
F_y=y
F_yM=M.y
F_yMM=M.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=שבוע w בשנת Y

I={0} – {1}

# labels of elements
L_era=תקופה
L_year=שנה
L_quarter=רבעון
L_month=חודש
L_week=שבוע
L_day=יום
L_weekday=יום בשבוע
L_dayperiod=לפנה״צ/אחה״צ
L_hour=שעה
L_minute=דקה
L_second=שנייה
L_zone=אזור
