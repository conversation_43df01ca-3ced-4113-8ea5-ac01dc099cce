# months
M(a)_1=ene.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=abr.
M(a)_5=may.
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=ago.
M(a)_9=sep.
M(a)_10=oct.

# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

# quarters
Q(w)_1=1.er trimestre
Q(w)_3=3.er trimestre

Q(W)_1=1.er trimestre
Q(W)_3=3.er trimestre

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(n)_morning2=mañana

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F(s)_d=d/M/y

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a
F_MMd=d/MM
F_yMM=MM/y
F_yQQQ=QQQ y

# labels of elements
L_dayperiod=a. m./p. m.

I={0} – {1}
