# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mar
M(a)_4=Apr
M(a)_5=May
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Aug
M(a)_9=Sep
M(a)_10=Oct
M(a)_11=Nov
M(a)_12=Dec

M(w)_1=January
M(w)_2=February
M(w)_3=March
M(w)_4=April
M(w)_5=May
M(w)_6=June
M(w)_7=July
M(w)_8=August
M(w)_9=September
M(w)_10=October
M(w)_11=November
M(w)_12=December

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

# weekdays
D(a)_1=Mon
D(a)_2=Tue
D(a)_3=Wed
D(a)_4=Thu
D(a)_5=Fri
D(a)_6=Sat
D(a)_7=Sun

D(s)_1=Mo
D(s)_2=Tu
D(s)_3=We
D(s)_4=Th
D(s)_5=Fr
D(s)_6=Sa
D(s)_7=Su

D(w)_1=Monday
D(w)_2=Tuesday
D(w)_3=Wednesday
D(w)_4=Thursday
D(w)_5=Friday
D(w)_6=Saturday
D(w)_7=Sunday

D(N)_1=M
D(N)_2=T
D(N)_3=W
D(N)_4=T
D(N)_5=F
D(N)_6=S
D(N)_7=S

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(w)_1=1st quarter
Q(w)_2=2nd quarter
Q(w)_3=3rd quarter
Q(w)_4=4th quarter

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=midnight
P(a)_am=am
P(a)_noon=noon
P(a)_pm=pm
P(a)_morning1=in the morning
P(a)_afternoon1=in the afternoon
P(a)_evening1=in the evening
P(a)_night1=at night

P(n)_midnight=mi
P(n)_am=a
P(n)_noon=n
P(n)_pm=p
P(n)_morning1=in the morning
P(n)_afternoon1=in the afternoon
P(n)_evening1=in the evening
P(n)_night1=at night

P(w)_midnight=midnight
P(w)_am=am
P(w)_noon=noon
P(w)_pm=pm
P(w)_morning1=in the morning
P(w)_afternoon1=in the afternoon
P(w)_evening1=in the evening
P(w)_night1=at night

P(A)_midnight=midnight
P(A)_am=AM
P(A)_noon=noon
P(A)_pm=PM
P(A)_morning1=morning
P(A)_afternoon1=afternoon
P(A)_evening1=evening
P(A)_night1=night

P(W)_midnight=midnight
P(W)_am=AM
P(W)_noon=noon
P(W)_pm=PM
P(W)_morning1=morning
P(W)_afternoon1=afternoon
P(W)_evening1=evening
P(W)_night1=night

# eras
E(w)_0=Before Christ
E(w|alt)_0=Before Common Era
E(w)_1=Anno Domini
E(w|alt)_1=Common Era

E(a)_0=BC
E(a|alt)_0=BCE
E(a)_1=AD
E(a|alt)_1=CE

E(n)_0=B
E(n)_1=A

# format patterns
F(f)_d=EEEE, MMMM d, y
F(l)_d=MMMM d, y
F(m)_d=MMM d, y
F(s)_d=M/d/yy

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} 'at' {0}
F(l)_dt={1} 'at' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=M/d
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='week' w 'of' Y

I={0} – {1}

# labels of elements
L_era=era
L_year=year
L_quarter=quarter
L_month=month
L_week=week
L_day=day
L_weekday=day of the week
L_dayperiod=am/pm
L_hour=hour
L_minute=minute
L_second=second
L_zone=time zone

# spanish era
E(n)_2=Era
E(a)_2=Era of Caesar
E(w)_2=Era of Caesar
