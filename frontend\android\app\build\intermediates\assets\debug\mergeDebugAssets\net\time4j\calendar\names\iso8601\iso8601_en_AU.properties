# months
M(A)_5=May

# weekdays
D(n)_1=M.
D(n)_2=Tu.
D(n)_3=W.
D(n)_4=Th.
D(n)_5=F.
D(n)_6=Sa.
D(n)_7=Su.

D(s)_1=Mon
D(s)_2=Tu
D(s)_3=Wed
D(s)_4=Th
D(s)_5=Fri
D(s)_6=Sat
D(s)_7=Su

D(N)_1=M.
D(N)_2=Tu.
D(N)_3=W.
D(N)_4=Th.
D(N)_5=F.
D(N)_6=Sa.
D(N)_7=Su.

D(S)_3=Wed
D(S)_5=Fri
D(S)_6=Sat
D(S)_1=Mon

# day-period-translations
P(a)_midnight=midnight
P(a)_noon=midday
P(a)_morning1=morning
P(a)_afternoon1=afternoon
P(a)_evening1=evening
P(a)_night1=night

P(n)_midnight=midnight
P(n)_am=am
P(n)_noon=midday
P(n)_pm=pm
P(n)_morning1=morning
P(n)_afternoon1=afternoon
P(n)_evening1=evening
P(n)_night1=night

P(w)_midnight=midnight
P(w)_noon=midday
P(w)_morning1=in the morning
P(w)_afternoon1=in the afternoon
P(w)_evening1=in the evening
P(w)_night1=at night

P(A)_noon=midday

P(N)_noon=midday

P(W)_noon=midday

# format patterns
F(s)_d=d/M/yy

F_Md=d/M
