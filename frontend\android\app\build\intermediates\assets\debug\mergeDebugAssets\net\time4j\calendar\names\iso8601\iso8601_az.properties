# months
M(a)_1=yan
M(a)_2=fev
M(a)_3=mar
M(a)_4=apr
M(a)_5=may
M(a)_6=iyn
M(a)_7=iyl
M(a)_8=avq
M(a)_9=sen
M(a)_10=okt
M(a)_11=noy
M(a)_12=dek

M(n)_1=1
M(n)_2=2
M(n)_3=3
M(n)_4=4
M(n)_5=5
M(n)_6=6
M(n)_7=7
M(n)_8=8
M(n)_9=9
M(n)_10=10
M(n)_11=11
M(n)_12=12

M(w)_1=yanvar
M(w)_2=fevral
M(w)_3=mart
M(w)_4=aprel
M(w)_5=may
M(w)_6=iyun
M(w)_7=iyul
M(w)_8=avqust
M(w)_9=sentyabr
M(w)_10=oktyabr
M(w)_11=noyabr
M(w)_12=dekabr

M(A)_1=yan
M(A)_2=fev
M(A)_3=mar
M(A)_4=apr
M(A)_5=may
M(A)_6=iyn
M(A)_7=iyl
M(A)_8=avq
M(A)_9=sen
M(A)_10=okt
M(A)_11=noy
M(A)_12=dek

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

M(W)_1=Yanvar
M(W)_2=Fevral
M(W)_3=Mart
M(W)_4=Aprel
M(W)_5=May
M(W)_6=İyun
M(W)_7=İyul
M(W)_8=Avqust
M(W)_9=Sentyabr
M(W)_10=Oktyabr
M(W)_11=Noyabr
M(W)_12=Dekabr

# weekdays
D(a)_1=B.E.
D(a)_2=Ç.A.
D(a)_3=Ç.
D(a)_4=C.A.
D(a)_5=C.
D(a)_6=Ş.
D(a)_7=B.

D(n)_1=1
D(n)_2=2
D(n)_3=3
D(n)_4=4
D(n)_5=5
D(n)_6=6
D(n)_7=7

D(s)_1=B.E.
D(s)_2=Ç.A.
D(s)_3=Ç.
D(s)_4=C.A.
D(s)_5=C.
D(s)_6=Ş.
D(s)_7=B.

D(w)_1=bazar ertəsi
D(w)_2=çərşənbə axşamı
D(w)_3=çərşənbə
D(w)_4=cümə axşamı
D(w)_5=cümə
D(w)_6=şənbə
D(w)_7=bazar

D(A)_1=B.E.
D(A)_2=Ç.A.
D(A)_3=Ç.
D(A)_4=C.A.
D(A)_5=C.
D(A)_6=Ş.
D(A)_7=B.

D(N)_1=1
D(N)_2=2
D(N)_3=3
D(N)_4=4
D(N)_5=5
D(N)_6=6
D(N)_7=7

D(S)_1=B.E.
D(S)_2=Ç.A.
D(S)_3=Ç.
D(S)_4=C.A.
D(S)_5=C.
D(S)_6=Ş.
D(S)_7=B.

D(W)_1=bazar ertəsi
D(W)_2=çərşənbə axşamı
D(W)_3=çərşənbə
D(W)_4=cümə axşamı
D(W)_5=cümə
D(W)_6=şənbə
D(W)_7=bazar

# quarters
Q(a)_1=1-ci kv.
Q(a)_2=2-ci kv.
Q(a)_3=3-cü kv.
Q(a)_4=4-cü kv.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-ci kvartal
Q(w)_2=2-ci kvartal
Q(w)_3=3-cü kvartal
Q(w)_4=4-cü kvartal

Q(A)_1=1-ci kv.
Q(A)_2=2-ci kv.
Q(A)_3=3-cü kv.
Q(A)_4=4-cü kv.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-ci kvartal
Q(W)_2=2-ci kvartal
Q(W)_3=3-cü kvartal
Q(W)_4=4-cü kvartal

# day-period-rules
T0000=night2
T0400=morning1
T0600=morning2
T1200=afternoon1
T1700=evening1
T1900=night1

# day-period-translations
P(a)_midnight=gecəyarı
P(a)_am=AM
P(a)_noon=günorta
P(a)_pm=PM
P(a)_morning1=sübh
P(a)_morning2=səhər
P(a)_afternoon1=gündüz
P(a)_evening1=axşamüstü
P(a)_night1=axşam
P(a)_night2=gecə

P(n)_midnight=gecəyarı
P(n)_am=a
P(n)_noon=g
P(n)_pm=p
P(n)_morning1=sübh
P(n)_morning2=səhər
P(n)_afternoon1=gündüz
P(n)_evening1=axşamüstü
P(n)_night1=axşam
P(n)_night2=gecə

P(w)_midnight=gecəyarı
P(w)_am=AM
P(w)_noon=günorta
P(w)_pm=PM
P(w)_morning1=sübh
P(w)_morning2=səhər
P(w)_afternoon1=gündüz
P(w)_evening1=axşamüstü
P(w)_night1=axşam
P(w)_night2=gecə

P(A)_midnight=gecəyarı
P(A)_am=AM
P(A)_noon=günorta
P(A)_pm=PM
P(A)_morning1=sübh
P(A)_morning2=səhər
P(A)_afternoon1=gündüz
P(A)_evening1=axşamüstü
P(A)_night1=axşam
P(A)_night2=gecə

P(N)_midnight=gecəyarı
P(N)_am=AM
P(N)_noon=günorta
P(N)_pm=PM
P(N)_morning1=sübh
P(N)_morning2=səhər
P(N)_afternoon1=gündüz
P(N)_evening1=axşamüstü
P(N)_night1=axşam
P(N)_night2=gecə

P(W)_midnight=gecəyarı
P(W)_am=AM
P(W)_noon=günorta
P(W)_pm=PM
P(W)_morning1=sübh
P(W)_morning2=səhər
P(W)_afternoon1=gündüz
P(W)_evening1=axşamüstü
P(W)_night1=axşam
P(W)_night2=gecə

# eras
E(w)_0=eramızdan əvvəl
E(w|alt)_0=bizim eradan əvvəl
E(w)_1=yeni era
E(w|alt)_1=bizim era

E(a)_0=e.ə.
E(a|alt)_0=b.e.ə.
E(a)_1=y.e.
E(a|alt)_1=b.e.

# format patterns
F(f)_d=d MMMM y, EEEE
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=Y, w 'həftə'

I={0} – {1}

# labels of elements
L_era=Era
L_year=İl
L_quarter=Rüb
L_month=Ay
L_week=Həftə
L_day=Gün
L_weekday=Həftənin Günü
L_dayperiod=AM/PM
L_hour=Saat
L_minute=Dəqiqə
L_second=Saniyə
L_zone=Saat Qurşağı
