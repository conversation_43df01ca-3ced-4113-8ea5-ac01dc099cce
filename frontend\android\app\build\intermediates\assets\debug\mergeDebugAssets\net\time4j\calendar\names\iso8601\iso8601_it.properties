# months
M(a)_1=gen
M(a)_2=feb
M(a)_3=mar
M(a)_4=apr
M(a)_5=mag
M(a)_6=giu
M(a)_7=lug
M(a)_8=ago
M(a)_9=set
M(a)_10=ott
M(a)_11=nov
M(a)_12=dic

M(n)_1=G
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=G
M(n)_7=L
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=gennaio
M(w)_2=febbraio
M(w)_3=marzo
M(w)_4=aprile
M(w)_5=maggio
M(w)_6=giugno
M(w)_7=luglio
M(w)_8=agosto
M(w)_9=settembre
M(w)_10=ottobre
M(w)_11=novembre
M(w)_12=dicembre

M(A)_1=gen
M(A)_2=feb
M(A)_3=mar
M(A)_4=apr
M(A)_5=mag
M(A)_6=giu
M(A)_7=lug
M(A)_8=ago
M(A)_9=set
M(A)_10=ott
M(A)_11=nov
M(A)_12=dic

M(N)_1=G
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=G
M(N)_7=L
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=gennaio
M(W)_2=febbraio
M(W)_3=marzo
M(W)_4=aprile
M(W)_5=maggio
M(W)_6=giugno
M(W)_7=luglio
M(W)_8=agosto
M(W)_9=settembre
M(W)_10=ottobre
M(W)_11=novembre
M(W)_12=dicembre

# weekdays
D(a)_1=lun
D(a)_2=mar
D(a)_3=mer
D(a)_4=gio
D(a)_5=ven
D(a)_6=sab
D(a)_7=dom

D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=G
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(s)_1=lun
D(s)_2=mar
D(s)_3=mer
D(s)_4=gio
D(s)_5=ven
D(s)_6=sab
D(s)_7=dom

D(w)_1=lunedì
D(w)_2=martedì
D(w)_3=mercoledì
D(w)_4=giovedì
D(w)_5=venerdì
D(w)_6=sabato
D(w)_7=domenica

D(A)_1=lun
D(A)_2=mar
D(A)_3=mer
D(A)_4=gio
D(A)_5=ven
D(A)_6=sab
D(A)_7=dom

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=G
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=lun
D(S)_2=mar
D(S)_3=mer
D(S)_4=gio
D(S)_5=ven
D(S)_6=sab
D(S)_7=dom

D(W)_1=lunedì
D(W)_2=martedì
D(W)_3=mercoledì
D(W)_4=giovedì
D(W)_5=venerdì
D(W)_6=sabato
D(W)_7=domenica

# quarters
Q(a)_1=T1
Q(a)_2=T2
Q(a)_3=T3
Q(a)_4=T4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1º trimestre
Q(w)_2=2º trimestre
Q(w)_3=3º trimestre
Q(w)_4=4º trimestre

Q(A)_1=T1
Q(A)_2=T2
Q(A)_3=T3
Q(A)_4=T4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1º trimestre
Q(W)_2=2º trimestre
Q(W)_3=3º trimestre
Q(W)_4=4º trimestre

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=mezzanotte
P(a)_am=AM
P(a)_noon=mezzogiorno
P(a)_pm=PM
P(a)_morning1=di mattina
P(a)_afternoon1=del pomeriggio
P(a)_evening1=di sera
P(a)_night1=di notte

P(n)_midnight=mezzanotte
P(n)_am=m.
P(n)_noon=mezzogiorno
P(n)_pm=p.
P(n)_morning1=di mattina
P(n)_afternoon1=del pomeriggio
P(n)_evening1=di sera
P(n)_night1=di notte

P(w)_midnight=mezzanotte
P(w)_am=AM
P(w)_noon=mezzogiorno
P(w)_pm=PM
P(w)_morning1=di mattina
P(w)_afternoon1=del pomeriggio
P(w)_evening1=di sera
P(w)_night1=di notte

P(A)_midnight=mezzanotte
P(A)_am=AM
P(A)_noon=mezzogiorno
P(A)_pm=PM
P(A)_morning1=mattina
P(A)_afternoon1=pomeriggio
P(A)_evening1=sera
P(A)_night1=notte

P(N)_midnight=mezzanotte
P(N)_am=m.
P(N)_noon=mezzogiorno
P(N)_pm=p.
P(N)_morning1=mattina
P(N)_afternoon1=pomeriggio
P(N)_evening1=sera
P(N)_night1=notte

P(W)_midnight=mezzanotte
P(W)_am=AM
P(W)_noon=mezzogiorno
P(W)_pm=PM
P(W)_morning1=mattina
P(W)_afternoon1=pomeriggio
P(W)_evening1=sera
P(W)_night1=notte

# eras
E(w)_0=avanti Cristo
E(w|alt)_0=avanti Era Volgare
E(w)_1=dopo Cristo
E(w|alt)_1=Era Volgare

E(a)_0=a.C.
E(a|alt)_0=a.E.V.
E(a)_1=d.C.
E(a|alt)_1=E.V.

E(n)_0=aC
E(n)_1=dC

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/yy

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='settimana' w 'del' Y

I={0} - {1}

# labels of elements
L_era=era
L_year=anno
L_quarter=trimestre
L_month=mese
L_week=settimana
L_day=giorno
L_weekday=giorno della settimana
L_dayperiod=AM/PM
L_hour=ora
L_minute=minuto
L_second=secondo
L_zone=fuso orario
