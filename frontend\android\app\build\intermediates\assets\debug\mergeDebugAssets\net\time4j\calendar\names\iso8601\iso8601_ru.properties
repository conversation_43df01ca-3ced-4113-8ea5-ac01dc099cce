# months
M(a)_1=янв.
M(a)_2=февр.
M(a)_3=мар.
M(a)_4=апр.
M(a)_5=мая
M(a)_6=июн.
M(a)_7=июл.
M(a)_8=авг.
M(a)_9=сент.
M(a)_10=окт.
M(a)_11=нояб.
M(a)_12=дек.

M(n)_1=Я
M(n)_2=Ф
M(n)_3=М
M(n)_4=А
M(n)_5=М
M(n)_6=И
M(n)_7=И
M(n)_8=А
M(n)_9=С
M(n)_10=О
M(n)_11=Н
M(n)_12=Д

M(w)_1=января
M(w)_2=февраля
M(w)_3=марта
M(w)_4=апреля
M(w)_5=мая
M(w)_6=июня
M(w)_7=июля
M(w)_8=августа
M(w)_9=сентября
M(w)_10=октября
M(w)_11=ноября
M(w)_12=декабря

M(A)_1=янв.
M(A)_2=февр.
M(A)_3=март
M(A)_4=апр.
M(A)_5=май
M(A)_6=июнь
M(A)_7=июль
M(A)_8=авг.
M(A)_9=сент.
M(A)_10=окт.
M(A)_11=нояб.
M(A)_12=дек.

M(N)_1=Я
M(N)_2=Ф
M(N)_3=М
M(N)_4=А
M(N)_5=М
M(N)_6=И
M(N)_7=И
M(N)_8=А
M(N)_9=С
M(N)_10=О
M(N)_11=Н
M(N)_12=Д

M(W)_1=январь
M(W)_2=февраль
M(W)_3=март
M(W)_4=апрель
M(W)_5=май
M(W)_6=июнь
M(W)_7=июль
M(W)_8=август
M(W)_9=сентябрь
M(W)_10=октябрь
M(W)_11=ноябрь
M(W)_12=декабрь

# weekdays
D(a)_1=пн
D(a)_2=вт
D(a)_3=ср
D(a)_4=чт
D(a)_5=пт
D(a)_6=сб
D(a)_7=вс

D(n)_1=пн
D(n)_2=вт
D(n)_3=ср
D(n)_4=чт
D(n)_5=пт
D(n)_6=сб
D(n)_7=вс

D(s)_1=пн
D(s)_2=вт
D(s)_3=ср
D(s)_4=чт
D(s)_5=пт
D(s)_6=сб
D(s)_7=вс

D(w)_1=понедельник
D(w)_2=вторник
D(w)_3=среда
D(w)_4=четверг
D(w)_5=пятница
D(w)_6=суббота
D(w)_7=воскресенье

D(A)_1=пн
D(A)_2=вт
D(A)_3=ср
D(A)_4=чт
D(A)_5=пт
D(A)_6=сб
D(A)_7=вс

D(N)_1=П
D(N)_2=В
D(N)_3=С
D(N)_4=Ч
D(N)_5=П
D(N)_6=С
D(N)_7=В

D(S)_1=пн
D(S)_2=вт
D(S)_3=ср
D(S)_4=чт
D(S)_5=пт
D(S)_6=сб
D(S)_7=вс

D(W)_1=понедельник
D(W)_2=вторник
D(W)_3=среда
D(W)_4=четверг
D(W)_5=пятница
D(W)_6=суббота
D(W)_7=воскресенье

# quarters
Q(a)_1=1-й кв.
Q(a)_2=2-й кв.
Q(a)_3=3-й кв.
Q(a)_4=4-й кв.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-й квартал
Q(w)_2=2-й квартал
Q(w)_3=3-й квартал
Q(w)_4=4-й квартал

Q(A)_1=1-й кв.
Q(A)_2=2-й кв.
Q(A)_3=3-й кв.
Q(A)_4=4-й кв.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-й квартал
Q(W)_2=2-й квартал
Q(W)_3=3-й квартал
Q(W)_4=4-й квартал

# day-period-rules
T0000=night1
T0400=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=полн.
P(a)_am=AM
P(a)_noon=полд.
P(a)_pm=PM
P(a)_morning1=утра
P(a)_afternoon1=дня
P(a)_evening1=вечера
P(a)_night1=ночи

P(n)_midnight=полн.
P(n)_am=AM
P(n)_noon=полд.
P(n)_pm=PM
P(n)_morning1=утра
P(n)_afternoon1=дня
P(n)_evening1=веч.
P(n)_night1=ночи

P(w)_midnight=полночь
P(w)_am=AM
P(w)_noon=полдень
P(w)_pm=PM
P(w)_morning1=утра
P(w)_afternoon1=дня
P(w)_evening1=вечера
P(w)_night1=ночи

P(A)_midnight=полн.
P(A)_am=AM
P(A)_noon=полд.
P(A)_pm=PM
P(A)_morning1=утро
P(A)_afternoon1=день
P(A)_evening1=веч.
P(A)_night1=ночь

P(N)_midnight=полн.
P(N)_am=AM
P(N)_noon=полд.
P(N)_pm=PM
P(N)_morning1=утро
P(N)_afternoon1=день
P(N)_evening1=веч.
P(N)_night1=ночь

P(W)_midnight=полночь
P(W)_am=AM
P(W)_noon=полдень
P(W)_pm=PM
P(W)_morning1=утро
P(W)_afternoon1=день
P(W)_evening1=вечер
P(W)_night1=ночь

# eras
E(w)_0=до Рождества Христова
E(w|alt)_0=до нашей эры
E(w)_1=от Рождества Христова
E(w|alt)_1=нашей эры

E(a)_0=до н. э.
E(a)_1=н. э.
E(a|alt)_1=н. э.

E(n)_0=до н.э.
E(n)_1=н.э.
E(n|alt)_1=н. э.

# format patterns
F(f)_d=EEEE, d MMMM y 'г'.
F(l)_d=d MMMM y 'г'.
F(m)_d=d MMM y 'г'.
F(s)_d=dd.MM.y

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMM=MM.y
F_yMMM=LLL y 'г'.
F_yMMMM=LLLL y 'г'.
F_yQQQ=QQQ y 'г'.
F_yQQQQ=QQQQ y 'г'.
F_yw=w-'я' 'неделя' Y 'г'.

I={0} – {1}

# labels of elements
L_era=эра
L_year=год
L_quarter=квартал
L_month=месяц
L_week=неделя
L_day=день
L_weekday=день недели
L_dayperiod=AM/PM
L_hour=час
L_minute=минута
L_second=секунда
L_zone=часовой пояс
