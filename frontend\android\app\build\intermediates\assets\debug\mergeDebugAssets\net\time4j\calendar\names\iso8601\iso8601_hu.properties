# months
M(a)_1=jan.
M(a)_2=febr.
M(a)_3=márc.
M(a)_4=ápr.
M(a)_5=máj.
M(a)_6=jún.
M(a)_7=júl.
M(a)_8=aug.
M(a)_9=szept.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=Á
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=Sz
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=január
M(w)_2=február
M(w)_3=március
M(w)_4=április
M(w)_5=május
M(w)_6=június
M(w)_7=július
M(w)_8=augusztus
M(w)_9=szeptember
M(w)_10=október
M(w)_11=november
M(w)_12=december

M(A)_1=jan.
M(A)_2=febr.
M(A)_3=márc.
M(A)_4=ápr.
M(A)_5=máj.
M(A)_6=jún.
M(A)_7=júl.
M(A)_8=aug.
M(A)_9=szept.
M(A)_10=okt.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=Á
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=Sz
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=január
M(W)_2=február
M(W)_3=március
M(W)_4=április
M(W)_5=május
M(W)_6=június
M(W)_7=július
M(W)_8=augusztus
M(W)_9=szeptember
M(W)_10=október
M(W)_11=november
M(W)_12=december

# weekdays
D(a)_1=H
D(a)_2=K
D(a)_3=Sze
D(a)_4=Cs
D(a)_5=P
D(a)_6=Szo
D(a)_7=V

D(n)_1=H
D(n)_2=K
D(n)_3=Sz
D(n)_4=Cs
D(n)_5=P
D(n)_6=Sz
D(n)_7=V

D(s)_1=H
D(s)_2=K
D(s)_3=Sze
D(s)_4=Cs
D(s)_5=P
D(s)_6=Szo
D(s)_7=V

D(w)_1=hétfő
D(w)_2=kedd
D(w)_3=szerda
D(w)_4=csütörtök
D(w)_5=péntek
D(w)_6=szombat
D(w)_7=vasárnap

D(A)_1=H
D(A)_2=K
D(A)_3=Sze
D(A)_4=Cs
D(A)_5=P
D(A)_6=Szo
D(A)_7=V

D(N)_1=H
D(N)_2=K
D(N)_3=Sz
D(N)_4=Cs
D(N)_5=P
D(N)_6=Sz
D(N)_7=V

D(S)_1=H
D(S)_2=K
D(S)_3=Sze
D(S)_4=Cs
D(S)_5=P
D(S)_6=Szo
D(S)_7=V

D(W)_1=hétfő
D(W)_2=kedd
D(W)_3=szerda
D(W)_4=csütörtök
D(W)_5=péntek
D(W)_6=szombat
D(W)_7=vasárnap

# quarters
Q(a)_1=I. n.év
Q(a)_2=II. n.év
Q(a)_3=III. n.év
Q(a)_4=IV. n.év

Q(n)_1=I.
Q(n)_2=II.
Q(n)_3=III.
Q(n)_4=IV.

Q(w)_1=I. negyedév
Q(w)_2=II. negyedév
Q(w)_3=III. negyedév
Q(w)_4=IV. negyedév

Q(A)_1=1. n.év
Q(A)_2=2. n.év
Q(A)_3=3. n.év
Q(A)_4=4. n.év

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=1. negyedév
Q(W)_2=2. negyedév
Q(W)_3=3. negyedév
Q(W)_4=4. negyedév

# day-period-rules
T0400=night2
T0600=morning1
T0900=morning2
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=éjfél
P(a)_am=de.
P(a)_noon=dél
P(a)_pm=du.
P(a)_morning1=reggel
P(a)_morning2=de.
P(a)_afternoon1=du.
P(a)_evening1=este
P(a)_night1=éjjel
P(a)_night2=hajnal

P(n)_midnight=éjfél
P(n)_am=de.
P(n)_noon=dél
P(n)_pm=du.
P(n)_morning1=reggel
P(n)_morning2=de.
P(n)_afternoon1=du.
P(n)_evening1=este
P(n)_night1=éjjel
P(n)_night2=hajnal

P(w)_midnight=éjfél
P(w)_am=de.
P(w)_noon=dél
P(w)_pm=du.
P(w)_morning1=reggel
P(w)_morning2=délelőtt
P(w)_afternoon1=délután
P(w)_evening1=este
P(w)_night1=éjjel
P(w)_night2=hajnal

P(A)_midnight=éjfél
P(A)_am=de.
P(A)_noon=dél
P(A)_pm=du.
P(A)_morning1=reggel
P(A)_morning2=de.
P(A)_afternoon1=du.
P(A)_evening1=este
P(A)_night1=éjjel
P(A)_night2=hajnal

P(N)_midnight=éjfél
P(N)_am=de.
P(N)_noon=dél
P(N)_pm=du.
P(N)_morning1=reggel
P(N)_morning2=de.
P(N)_afternoon1=du.
P(N)_evening1=este
P(N)_night1=éjjel
P(N)_night2=hajnal

P(W)_midnight=éjfél
P(W)_am=de.
P(W)_noon=dél
P(W)_pm=du.
P(W)_morning1=reggel
P(W)_morning2=délelőtt
P(W)_afternoon1=délután
P(W)_evening1=este
P(W)_night1=éjjel
P(W)_night2=hajnal

# eras
E(w)_0=Krisztus előtt
E(w|alt)_0=időszámításunk előtt
E(w)_1=időszámításunk szerint
E(w|alt)_1=i. sz.

E(a)_0=i. e.
E(a|alt)_0=i.e.
E(a)_1=i. sz.
E(a|alt)_1=i.sz.

E(n)_0=ie.
E(n)_1=isz.

# format patterns
F(f)_d=y. MMMM d., EEEE
F(l)_d=y. MMMM d.
F(m)_d=y. MMM d.
F(s)_d=y. MM. dd.

F(alt)=H:mm:ss

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=a h
F_H=H
F_hm=a h:mm
F_Hm=H:mm
F_hms=a h:mm:ss
F_Hms=H:mm:ss

F_Md=M. d.
F_MMMd=MMM d.
F_MMMMd=MMMM d.
F_y=y.
F_yM=y. M.
F_yMMM=y. MMM
F_yMMMM=y. MMMM
F_yQQQ=y. QQQ
F_yQQQQ=y. QQQQ
F_yw=Y w. 'hete'

I={0} – {1}

# labels of elements
L_era=éra
L_year=év
L_quarter=negyedév
L_month=hónap
L_week=hét
L_day=nap
L_weekday=hét napja
L_dayperiod=napszak
L_hour=óra
L_minute=perc
L_second=másodperc
L_zone=időzóna
