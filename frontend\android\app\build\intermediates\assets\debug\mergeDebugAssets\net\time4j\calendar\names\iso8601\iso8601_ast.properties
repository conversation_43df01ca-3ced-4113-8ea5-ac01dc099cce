# months
M(a)_1=xin
M(a)_2=feb
M(a)_3=mar
M(a)_4=abr
M(a)_5=may
M(a)_6=xun
M(a)_7=xnt
M(a)_8=ago
M(a)_9=set
M(a)_10=och
M(a)_11=pay
M(a)_12=avi

M(n)_1=X
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=X
M(n)_7=X
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=P
M(n)_12=A

M(w)_1=de xineru
M(w)_2=de febreru
M(w)_3=de marzu
M(w)_4=d’abril
M(w)_5=de mayu
M(w)_6=de xunu
M(w)_7=de xunetu
M(w)_8=d’agostu
M(w)_9=de setiembre
M(w)_10=d’ochobre
M(w)_11=de payares
M(w)_12=d’avientu

M(A)_1=Xin
M(A)_2=Feb
M(A)_3=Mar
M(A)_4=Abr
M(A)_5=May
M(A)_6=Xun
M(A)_7=Xnt
M(A)_8=Ago
M(A)_9=Set
M(A)_10=Och
M(A)_11=Pay
M(A)_12=Avi

M(N)_1=X
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=X
M(N)_7=X
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=P
M(N)_12=A

M(W)_1=xineru
M(W)_2=febreru
M(W)_3=marzu
M(W)_4=abril
M(W)_5=mayu
M(W)_6=xunu
M(W)_7=xunetu
M(W)_8=agostu
M(W)_9=setiembre
M(W)_10=ochobre
M(W)_11=payares
M(W)_12=avientu

# weekdays
D(a)_1=llu
D(a)_2=mar
D(a)_3=mié
D(a)_4=xue
D(a)_5=vie
D(a)_6=sáb
D(a)_7=dom

D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=X
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(s)_1=ll
D(s)_2=ma
D(s)_3=mi
D(s)_4=xu
D(s)_5=vi
D(s)_6=sá
D(s)_7=do

D(w)_1=llunes
D(w)_2=martes
D(w)_3=miércoles
D(w)_4=xueves
D(w)_5=vienres
D(w)_6=sábadu
D(w)_7=domingu

D(A)_1=llu
D(A)_2=mar
D(A)_3=mié
D(A)_4=xue
D(A)_5=vie
D(A)_6=sáb
D(A)_7=dom

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=X
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=ll
D(S)_2=ma
D(S)_3=mi
D(S)_4=xu
D(S)_5=vi
D(S)_6=sá
D(S)_7=do

D(W)_1=llunes
D(W)_2=martes
D(W)_3=miércoles
D(W)_4=xueves
D(W)_5=vienres
D(W)_6=sábadu
D(W)_7=domingu

# quarters
Q(a)_1=1T
Q(a)_2=2T
Q(a)_3=3T
Q(a)_4=4T

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1er trimestre
Q(w)_2=2u trimestre
Q(w)_3=3er trimestre
Q(w)_4=4u trimestre

Q(A)_1=1T
Q(A)_2=2T
Q(A)_3=3T
Q(A)_4=4T

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1er trimestre
Q(W)_2=2u trimestre
Q(W)_3=3er trimestre
Q(W)_4=4u trimestre

# day-period-translations
P(a)_am=AM
P(a)_pm=PM

P(n)_am=a
P(n)_pm=p

P(w)_am=de la mañana
P(w)_pm=de la tarde

P(A)_am=AM
P(A)_pm=PM

P(N)_am=a
P(N)_pm=p

P(W)_am=mañana
P(W)_pm=tarde

# eras
E(w)_0=enantes de Cristu
E(w|alt)_0=enantes de la dómina común
E(w)_1=después de Cristu
E(w|alt)_1=dómina común

E(a)_0=e.C.
E(a|alt)_0=EDC
E(a)_1=d.C.
E(a|alt)_1=DC

# format patterns
F(f)_d=EEEE, d MMMM 'de' y
F(l)_d=d MMMM 'de' y
F(m)_d=d MMM y
F(s)_d=d/M/yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'a' 'les' {0}
F(l)_dt={1} 'a' 'les' {0}
F(m)_dt={1}, {0}
F(s)_dt={1} {0}
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=LLLL 'de' y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ 'de' y
F_yw='selmana' w 'de' Y

I={0} – {1}

# labels of elements
L_era=era
L_year=añu
L_quarter=trimestre
L_month=mes
L_week=selmana
L_day=día
L_weekday=día de la selmana
L_dayperiod=periodu del día
L_hour=hora
L_minute=minutu
L_second=segundu
L_zone=estaya horaria
